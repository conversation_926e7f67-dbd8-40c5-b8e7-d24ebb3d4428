<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.4.RELEASE</version>
        <relativePath/> <!-- lookup parent from repository -->
    </parent>
    <groupId>com.tfrunning</groupId>
    <artifactId>tfrunningGW</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <name>tfrunning-gw</name>
    <description>tfrunning-gw</description>


    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <dubbo-springboot.version>0.2.0</dubbo-springboot.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.10</version>
        </dependency>

        <dependency><!-- 此依赖用于亚马逊 S3 -->
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>s3</artifactId>
            <version>2.14.14</version>
        </dependency>

        <dependency><!-- 用于解析微信xml消息 -->
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.9.8</version>
        </dependency>

        <dependency><!-- 此依赖用于SM2加密解密 -->
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.54</version>
        </dependency>

        <!--pdf转换-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>2.0.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.1</version>
        </dependency>

        <!-- redis -->
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.0</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!-- http -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.11</version>
        </dependency>

        <dependency><!-- 此依赖用于通讯 -->
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>

        <dependency><!-- 此依赖用于httpclient -->
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.10</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 腾讯云 -->
        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>qcloud-image-sdk</artifactId>
            <version>2.3.3</version>
        </dependency>

        <dependency><!-- 此依赖用于ifms-tf.mcs.service dubbo调用 -->
            <groupId>com.tfrunning</groupId>
            <artifactId>ifms-service</artifactId>
            <version>1.0.1</version>
        </dependency>

        <!-- dubbo 环境包更换 -->
        <dependency><!-- 此依赖用于dubbo -->
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
            <version>${dubbo-springboot.version}</version>
        </dependency>

        <dependency><!-- 此依赖用于dubbo -->
            <groupId>com.alibaba.boot</groupId>
            <artifactId>dubbo-spring-boot-actuator</artifactId>
            <version>${dubbo-springboot.version}</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.0</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.25</version>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
        </dependency>

        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>5.2.0</version>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.3.3</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.58</version>
        </dependency>

        <dependency><!-- 此依赖用于json数据结构转换 -->
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>

        <!-- 此依赖用于文件上传 -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.1</version>
        </dependency>

        <!-- 发送邮件 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dingtalk</groupId>
            <artifactId>taobao-sdk</artifactId>
            <version>20230518</version>
        </dependency>
        <dependency>
            <groupId>com.laiwang.lippi</groupId>
            <artifactId>lippi.oapi.encryt</artifactId>
            <version>1.0.4-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea</artifactId>
            <version>1.2.9</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-openapi</artifactId>
            <version>0.1.3</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>tea-util</artifactId>
            <version>0.2.13</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>credentials-java</artifactId>
            <version>0.2.4</version>
        </dependency>


        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.9.4</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>dingtalk</artifactId>
            <version>1.1.86</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.12.13</version>
        </dependency>


    </dependencies>


    <build>
        <finalName>tfrunningGW</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.12.RELEASE</version>
            </plugin>
        </plugins>

    </build>


    <profiles>
        <profile>
            <id>package</id>
            <build>
                <resources>
                    <resource>
                        <directory>res</directory>
                        <excludes>
                            <exclude>**/*</exclude>
                        </excludes>
                    </resource>
                </resources>

                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>appassembler-maven-plugin</artifactId>
                        <version>2.1.0</version>
                        <configuration>
                            <platforms>
                                <platform>windows</platform>
                                <platform>unix</platform>
                            </platforms>
                            <assembleDirectory>${project.build.directory}/tfrunningGW</assembleDirectory>
                            <programs>
                                <program>
                                    <mainClass>com.tfrunning.gw.GwApplication</mainClass>
                                    <name>tfrunningGW</name>
                                    <jvmSettings>
                                        <extraArguments>
                                            <extraArgument>-Xms512M</extraArgument>
                                            <extraArgument>-Xmx2048M</extraArgument>
                                            <extraArgument>-XX:+HeapDumpOnOutOfMemoryError</extraArgument>
                                            <extraArgument>-XX:HeapDumpPath=logs/tfrunningGW.hprof</extraArgument>
                                        </extraArguments>
                                    </jvmSettings>
                                </program>
                            </programs>
                            <useWildcardClassPath>true</useWildcardClassPath>
                            <encoding>UTF-8</encoding>
                            <binFolder>bin</binFolder>
                            <repositoryLayout>flat</repositoryLayout>
                            <repositoryName>lib</repositoryName>
                            <configurationDirectory>conf</configurationDirectory>
                            <configurationSourceDirectory>src/main/resources</configurationSourceDirectory>
                            <copyConfigurationDirectory>true</copyConfigurationDirectory>
                            <includeConfigurationDirectoryInClasspath>true</includeConfigurationDirectoryInClasspath>
                        </configuration>
                        <executions>
                            <execution>
                                <phase>package</phase>
                                <goals>
                                    <goal>assemble</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>

                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-antrun-plugin</artifactId>
                        <version>3.1.0</version>
                        <executions>
                            <execution>
                                <id>package-service</id>
                                <phase>package</phase>
                                <goals>
                                    <goal>run</goal>
                                </goals>
                                <configuration>
                                    <target>
                                        <tar destfile="${project.build.directory}/tfrunningGW.tgz" compression="gzip">
                                            <tarfileset dir="${project.build.directory}/tfrunningGW" filemode="755">
                                                <include name="bin/**"/>
                                            </tarfileset>
                                            <tarfileset dir="${project.build.directory}/tfrunningGW">
                                                <exclude name="bin/**"/>
                                            </tarfileset>
                                        </tar>
                                    </target>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>


    <repositories>
        <repository>
            <id>aliyun-maven</id>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
        <repository>
            <id>gitlab-maven</id>
            <url>http://10.1.6.6/api/v4/projects/19/packages/maven</url>
        </repository>
    </repositories>

    <distributionManagement>
        <repository>
            <id>gitlab-maven</id>
            <url>http://10.1.6.6/api/v4/projects/19/packages/maven</url>
        </repository>
        <snapshotRepository>
            <id>gitlab-maven</id>
            <url>http://10.1.6.6/api/v4/projects/19/packages/maven</url>
        </snapshotRepository>
    </distributionManagement>

</project>
