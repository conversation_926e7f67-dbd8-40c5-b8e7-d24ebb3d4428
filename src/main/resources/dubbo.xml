<?xml version="1.0" encoding="UTF-8" ?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans-4.3.xsd
       http://code.alibabatech.com/schema/dubbo
       http://code.alibabatech.com/schema/dubbo/dubbo.xsd"
       default-lazy-init="false" default-autowire="byName">

    <!-- 提供方应用信息，用于计算依赖关系 -->
    <dubbo:application name="tfrunningGW" />

    <!-- 配置dubbo服务寻找机制，0无限重试 -->
    <dubbo:provider timeout="30000" retries="2" />

    <!--************** 羊羊羊-->

    <!-- 多注册中心配置 -->
    <dubbo:registry protocol="zookeeper" address="*************:2048" id="mcs" />

    <!--作为客户端生成远程服务代理，可以和本地bean一样使用Service -->
    <dubbo:reference id="IIfmsService" interface="tf.mcs.service.IIfmsService" registry="mcs" check="false" />

    <!-- 用于微贷系统连接gateway -->
    <dubbo:registry address="N/A" id="local"/>

    <dubbo:protocol name="dubbo" host="127.0.0.1" port="20881"/>

    <dubbo:service interface="tf.mcs.service.JnPushService" ref="jnPushService" registry="local"/>

    <bean id="jnPushService" class="tf.mcs.service.impl.JnPushServiceImpl"/>

</beans>