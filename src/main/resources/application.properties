server.port=8008
#server.servlet.contextPath=/microcred
spring.main.allow-bean-definition-overriding=true

#上传文件
spring.servlet.multipart.enabled=true
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=100MB

#静态文件
spring.mvc.static-path-pattern=/web/**
spring.resources.static-locations=classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/

#redis
#测试
spring.redis.host=*************
spring.redis.password=mxmcs
#本地
spring.redis.port=6666
spring.redis.database=11
spring.redis.jedis.pool.max-idle=8
spring.redis.jedis.pool.min-idle=0
spring.redis.jedis.pool.max-wait=-1

#IdWorker
custom.snowflake.workerid=1
custom.snowflake.datacenterid=2

#上传
custom.uploadfilepath=/home/<USER>/data/tfrunningGW/UploadFiles/

custom.publickey="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCAX52IsJGV75KLV95zlixfGCtY/Lr5WLs/lphRIFDFaqtKGVX+scF+ilh/FvobxPn2/JemDfmdw4dFWxtqMqqZOgzTyh9pxJ8eNqubvO7x/YdKu2Y9+klQErFb01Ii5TsG8KczArd5M7yZexwKKEoZpwdX5bmiQsHhkYL32dz3PwIDAQAB"

#微信
weichat.app_id=wx22b0fd1f67eb2522
weichat.secret_key=bfc4566ab25c857d1b3086d96033c7a7
weichat.tx_token=sittoken123654

weichat.url=http://wechat.microcredchina.com/sitmcsweb/user
weichat.signurl=http://wechat.microcredchina.com/sitmcsweb/SignList

#优惠券地址
weichat.couponurl=http://wechat.microcredchina.com/sitmcsweb/newcoupon

#weichat.signtemplate=d43uuabN6KZlOdzikRXp47CRK9dyagLCHBXHHO0eGc8
#weichat.template=WgqrZXbrZPwdpuE86wmE86U5m5y6eIny2oLTC0eIBFg

#微信消息模板-贷款到期提醒-简化续贷
weichat.template.renew=WgqrZXbrZPwdpuE86wmE86U5m5y6eIny2oLTC0eIBFg
#微信消息模板-待签约合同提醒
weichat.template.sign=H03w3L4wEV0iMPlmWgmRTEgMMFAdMm4hm6SsyaU9V2Q

#微信消息模板-还款提醒
weichat.template.repay=BTTEW3c1bjhbXu1ExsfVH8ReQKrhwsOcrOriAFKaYC8
#微信消息模板-逾期提醒
weichat.template.lnlo=jQkKbPJwQsCIvjevaHi2BsIzEdW-80AkoYYCiwRQ4j8

#微信消息模板-优惠券到期提醒
weichat.template.couponlo=umCxRL3yEJYY55Z3D7orYemJcgCV5cbQGAPvnTV_rlw

#微信支付-支付结果通知URL
weichat.pay.notify_url=https://wechat.microcredchina.com/microcred/wxpay/paynotify-1


#秘钥文件
#请求解密
encrypt.privateKey=5dac604d60d6076abde49c20c0e327cc8b9c23f0792120c65df1b6ceaa7fea32
#响应加密
encrypt.publicKey=0466ca23e34a7eaea3d35fda722688568f224e6311e676f5b665c39bfd57e1d1e76c46bbcefa9f1f6302d7ad226a585a833b30c5f99c353b9e5f3f26c22b0748b5

#亚马逊
media.aws.usekey=false
media.aws.access_key_id=********************
media.aws.secret_access_key=KRS4wtISqdBK/ybWB1OSqF772Nf/J57C55vOVnO6
media.s3.bucket=testmcs
media.s3.region=cn-northwest-1

#邮箱配置
#平台地址，这里用的是qq邮箱，使用其他邮箱请更换
spring.mail.host=smtp.qq.com
spring.mail.protocol=smtps
spring.mail.port=465
spring.mail.default-encoding=UTF-8
#发送邮件的邮箱地址：改成自己的邮箱
spring.mail.username=<EMAIL>
#发送短信后它给你的授权码 填写到这里
spring.mail.password=wlojgpipkbjccaij
#与发件邮箱一致
spring.mail.from=<EMAIL>
#下面不用改
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true
spring.mail.properties.mail.smtp.starttls.required=true

#百度 测试帐号
#baidu.app_id=24510399
#baidu.api_key=SxHqdCLOdzpBjEEuYZ6CNjk0
#baidu.secret_key=xacPPSj0PknqH4CQ2sIRbfLW33oOiacS
#baidu.plan_id=12088

#百度 美兴提供
baidu.app_id=24278231
baidu.api_key=UuNuBngOkXB5yn5UXgsT2dGn
baidu.secret_key=9jSiMYNZgZy63eeBGUK4NAjdLLiQcxoZ
#baidu.plan_id=11812
baidu.plan_id=15276
baidu.plan_id_simple=11793

#native支付回调地址
native.notify_url=http://wechat.microcredchina.com/SitMicrocred/wxpay/payNotifyForNative
native.notify_url_s=http://wechat.microcredchina.com/SitMicrocred/wxpay/payNotifyForNativeSC

#pad扫码登录
dingding.AppKey=dingk2yycjcmbz7vbrtl
dingding.AppSecret=4qFFIvM75F7nXV_kZWnevQ2K-JGymSRnyW--KoZWfgBFbjitNUyfvIFv767nARnN

# 置信度
ocr.confidence=2.5

# Dubbo Configuration
# 应用配置
dubbo.application.name=tfrunningGW

# Provider配置 - 服务提供者超时和重试设置
dubbo.provider.timeout=30000
dubbo.provider.retries=2

# 多注册中心配置
# 主注册中心 - MCS系统
dubbo.registries.mcs.protocol=zookeeper
dubbo.registries.mcs.address=*************:2048
dubbo.registries.mcs.id=mcs

# 本地注册中心 - 用于微贷系统连接gateway
dubbo.registries.local.address=N/A
dubbo.registries.local.id=local

# 协议配置
dubbo.protocol.name=dubbo
dubbo.protocol.host=127.0.0.1
dubbo.protocol.port=20881

# 服务引用配置 - 作为客户端生成远程服务代理
dubbo.consumer.references.IIfmsService.interface=tf.mcs.service.IIfmsService
dubbo.consumer.references.IIfmsService.registry=mcs
dubbo.consumer.references.IIfmsService.check=false

# 服务暴露配置 - 暴露JnPushService到本地注册中心
dubbo.provider.services.jnPushService.interface=tf.mcs.service.JnPushService
dubbo.provider.services.jnPushService.ref=jnPushService
dubbo.provider.services.jnPushService.registry=local


# baiduerrorcode.properties
222350=公安网图片不存在或质量过低
222351=身份证号与姓名不匹配
216601=身份证号和名字匹配失败

#customservice.properties
#自动客服恢复模板
customservice.reply.hi=Hi \r\n感谢关注美兴小贷官方微信！\r\n18年诚信经营，全国优秀小额贷款公司，最高信用贷款金额为150万元！\r\n-----------------\r\n回复序列号了解详细内容：\r\n1.查询各业网点服务电话\r\n2.贷款申请方式\r\n3.查询贷款办理进度\r\n4.贷款结清\r\n5.解除绑定\r\n6.客户经理\r\n7.还款\r\n8.投诉及建议\r\n \r\n*若人工回复不及时，请您拨打服务热线\r\n**********（工作日09：00-12：00、13：30-18：00）\r\n
customservice.reply.1=网点电话查看方式：\r\n美兴微信公众号-走进美兴（页面左下角）-联系我们.\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.2=贷款申请方式：\r\n(1)微信公众号首页—贷款业务（页面右下角）—申请贷款-填写资料提交-等待专属客户经理主动联系您\r\n(2)拨打服务热线**********（工作日09：00-12：00、13：30-18：00）\r\n
customservice.reply.3=贷款办理进度查询：\r\n微信公众号首页—贷款业务（页面右下角）—贷款进度。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.4=贷款结清办理：\r\n请联系您的专属客户经理咨询和办理（回复“客户经理”系统会推送联系方式给您）\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.5=微信公众号解除绑定方法：在对话框中回复“我要解绑”\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.6=您可以通过以下三种方式：\r\n（1）微信公众号查询：贷款业务→点击【我的贷款】→验证身份后即可查询还款及其他贷款讯息；\r\n（2） 致电您的【专属客户经理】，查询还款情况； \r\n（3） 拨打服务热线：**********，服务时间为周一至周五（09:00 -18:00）。
customservice.reply.7=您好，还款日当天我公司会向您的签约代扣账户发起扣款，若扣款不成功请及时联系您的客户经理查看原因（回复“客户经理”系统会推送联系方式给您）如果是提前结清贷款也请联系您的客户经理处理。\r\n \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.8=投诉建议方式：\r\n（1）拨打服务热线**********（工作日09：00-12：00、13：30-18：00）\r\n（2）公众号-贷款业务-投诉建议\r\n（3）邮箱：<EMAIL>\r\n
customservice.reply.9=逾期还款记录将会上传人民银行征信系统，对您的征信造成不良影响。请根据合同约定的时间及还款金额按时还款，珍惜您的信用记录。
customservice.reply.10=
customservice.reply.11=现场投诉：直接向营业网点负责人投诉\r\n电话投诉：**********\r\n邮箱投诉：<EMAIL>
customservice.reply.repay=您好，请点击 <a href="https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx5a8134a9d7af0674&redirect_uri=https%3A%2F%2Fwechatoa.microcredchina.com%2Fmicroweb%2FrepayTabs&response_type=code&scope=snsapi_base#wechat_redirect">【我的还款】</a>，进行还款
customservice.reply.13=您好，请联系您的专属客户经理开具结清证明，回复“客户经理”系统会推送联系方式给您。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.14=您好，我公司贷款额度为1-150万，年化利率10.2%起，除了合同约定的息费，没有其他隐形费用。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.15=您好，推荐码请填写推荐人的手机号，若您没有推荐人不用填写。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.16=您好，贷款审批会在正式提出贷款申请后三个工作日内出结果，具体办理进度和额度请联系您的专属客户经理咨询，回复“客户经理”系统会推送联系方式给您。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.17=您好，美兴小额贷款目前的业务范围在四川省内，外省暂时不能办理。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.18=您好，若已经有工作人员为您办理贷款申请，请直接告知工作人员取消贷款，若还未开始办理不再继续申请即可。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.19=您好，您的还款记录我公司会按照规定如实上传，逾期会造成征信不良记录及罚息。如有特殊情况请联系您的专属客户经理（回复“客户经理”系统会推送联系方式给您）。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.20=优惠券领取方式请查看：美兴微信公众号-使用指南-《我的优惠劵该如何使用？》您也可以联系专属客户经理咨询（回复“客户经理”系统会推送联系方式给您）。 \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.21=还款记录查询方式：\r\n美兴微信公众号-贷款业务-我的贷款-还款查询。\r\n \r\n有其他疑问请联系服务热线**********（工作日09：00-12：00、13：30-18：00
customservice.reply.22=贷款合同查询方式：\r\n1、（电子合同）美兴小额贷款微信公众号-贷款业务-我的贷款-待签订合同-已签约文件；\r\n2、（纸质合同）请通过签订的贷款合同查询。\r\n您也可以联系您的专属客户经理（回复“客户经理”系统会推送联系方式给您）， \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）\r\n
customservice.reply.23=您好，在美兴贷款除了合同约定的息费，没有其他隐形费用。\r\n \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.24=微信公众号绑定方法：\r\n点击首页右下角贷款业务-我的贷款-输入个人信息获取短信验证码-提交即可。\r\n \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.25=我的贷款信息查询方式：\r\n（1） 微信公众号：贷款业务-我的贷款\r\n（2） 请联系您的专属客户经理查询（回复“客户经理”系统会推送联系方式给您）\r\n \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）
customservice.reply.26=您好，请联系您的专属客户经理办理（回复“客户经理”系统会推送联系方式给您）。\r\n \r\n有其他疑问请拨打服务热线**********（工作日09：00-12：00、13：30-18：00）