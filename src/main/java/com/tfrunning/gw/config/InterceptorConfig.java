package com.tfrunning.gw.config;


import com.tfrunning.gw.config.intercepors.LoginInterceptor;
import com.tfrunning.gw.config.intercepors.WebInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {

    @Autowired
    private LoginInterceptor loginInterceptor;
    @Autowired
    private WebInterceptor webInterceptor;

    // 这个方法用来注册拦截器，我们自己写好的拦截器需要通过这里添加注册才能生效
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(loginInterceptor).addPathPatterns("/pad/**").excludePathPatterns("/error","/web/**","/pad/changepass");//web为静态资源
        registry.addInterceptor(webInterceptor).addPathPatterns("/wechat/**").excludePathPatterns("/wechat/getOp","/error","/web/**","/wechat/getTimestamp","/dingtalk/**","/wechat/getGrabCouponList");
    }

}
