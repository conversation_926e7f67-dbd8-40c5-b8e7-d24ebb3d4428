package com.tfrunning.gw.config.intercepors;

import com.tfrunning.gw.utils.DESEncrypt;
import com.tfrunning.gw.utils.RedisTools;
import com.tfrunning.gw.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class WebInterceptor implements HandlerInterceptor {

    private static Logger logger = LoggerFactory.getLogger(WebInterceptor.class);

    @Autowired
    private RedisTools redisTools;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object object) {

        logger.info("========网页拦截器开始========");

        //判断上传的openid，wechatid与redis中是否一样
        String openid = (String) request.getAttribute("opname");
        String wechatid = (String) request.getAttribute("sername");

        logger.info(String.format("传入openid为:%s，token为:%s",openid,wechatid));

        if(!Tools.isNull(openid)){
            openid = DESEncrypt.decrypt(openid);

            String token = redisTools.hget(openid,"token");
            if(!Tools.isNull(token) && token.equals(wechatid)){
                return true;
            }

        }

        RespTfData respTfData = new RespTfData();

        respTfData.setRespCode("8888");
        respTfData.setRespDesc("登录状态已失效，请重新登录！");

        Tools.sentData(response,respTfData);
        return false;
    }

}
