package com.tfrunning.gw.config.intercepors;

import com.tfrunning.gw.utils.TokenTools;
import com.tfrunning.gw.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class LoginInterceptor implements HandlerInterceptor {

    private static Logger logger = LoggerFactory.getLogger(LoginInterceptor.class);

    @Autowired
    private TokenTools tokenTools;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object object) {

        logger.info("========pad拦截器开始========");

        //判断token
        String token = (String) request.getAttribute("token");

        logger.info(String.format("传入token为:%s",token));

        if(!Tools.isNull(token)){
            Boolean auth = tokenTools.checkTokenWithExpire(token);
            if(auth){
                return true;
            }

        }

        RespTfData respTfData = new RespTfData();

        respTfData.setRespCode("8888");
        respTfData.setRespDesc("登录状态已失效，请重新登录！");

        Tools.sentData(response,respTfData);
        return false;
    }

}
