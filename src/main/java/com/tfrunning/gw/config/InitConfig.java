package com.tfrunning.gw.config;

import com.tfrunning.gw.utils.IdWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

@Configuration
public class InitConfig {
    private static Logger logger = LoggerFactory.getLogger(InitConfig.class);

    @Autowired
    private Environment environment;

    @Bean
    public IdWorker idWorker() {
        Long workerid = Long.valueOf(environment.getProperty("custom.snowflake.workerid"));
        Long datacenterid = Long.valueOf(environment.getProperty("custom.snowflake.datacenterid"));
        return new IdWorker(workerid, datacenterid);
    }
}
