package com.tfrunning.gw.config.filter;

import com.alibaba.fastjson.JSON;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.dingtalk.oapi.lib.aes.Utils;
import com.tfrunning.gw.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@WebFilter
public class DecryptFilter implements Filter {

    private static Logger logger = LoggerFactory.getLogger(DecryptFilter.class);

    private static String privateKey;

    @Value("${encrypt.privateKey}")
    public void setPrivateKey(String privateKey) {
        this.privateKey=privateKey;
    }

    @Autowired
    Tools tools;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
    }

    @Override
    public void destroy() {
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {

        HttpServletRequest req = (HttpServletRequest) request;
        String requestURI = req.getRequestURI();
        OutputStream outputStream = null;
//        logger.info("--------------------->过滤器：请求地址"+requestURI);
        if(requestURI.contains("/user") || requestURI.contains("/pad")|| requestURI.contains("/baidu")|| requestURI.contains("/wechat")){//加密平台
            DecryptServletResponseWrapper mResp = new DecryptServletResponseWrapper((HttpServletResponse) response); // 包装响应对象 resp 并缓存响应数据
            chain.doFilter(new DecryptServletRequestWrapper((HttpServletRequest) request,privateKey), mResp);
            try {
                DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY, Constant.CORP_ID);
                //logger.info("--------------------->response加密前byte-length:"+mResp.getBytes().length+";byte:"+mResp.getBytes());
                String encrypt = new String(mResp.getBytes(),"utf-8");
                logger.info("--------------------->response加密前encrypt-legnth:"+encrypt.length());
                Map<String, String> result = dingTalkEncryptor.getEncryptedMap(encrypt, System.currentTimeMillis(), Utils.getRandomStr(8));
                String json = JSON.toJSONString(result);
                logger.info("--------------------->response加密后json-length:"+json.length());
                // 获取输出流对象
                outputStream = response.getOutputStream();
                outputStream.write(json.getBytes(StandardCharsets.UTF_8));
                outputStream.flush();
            } catch (DingTalkEncryptException e) {
                e.printStackTrace();
            }finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        }else{
            chain.doFilter(request, response);
        }
    }
}
