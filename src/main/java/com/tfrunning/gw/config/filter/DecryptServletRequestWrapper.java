package com.tfrunning.gw.config.filter;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptException;
import com.dingtalk.oapi.lib.aes.DingTalkEncryptor;
import com.dingtalk.oapi.lib.aes.Utils;
import com.tfrunning.gw.utils.Tools;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ReadListener;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * 对参数进行解密
 */
public class DecryptServletRequestWrapper extends HttpServletRequestWrapper {

    private static Logger logger = LoggerFactory.getLogger(DecryptServletRequestWrapper.class);
    private String body = "";
    String privateKey;

    private Map<String, String[]> params = new HashMap<String, String[]>();

    public DecryptServletRequestWrapper(HttpServletRequest servletRequest,String privateKey) {
        super(servletRequest);
        this.params.putAll(servletRequest.getParameterMap());

        this.privateKey = privateKey;

        //创建字符缓冲区
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        InputStream inputStream = null;
        try {
            inputStream = servletRequest.getInputStream();
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                //将输入流里面的参数读取到字符缓冲区
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }

            //s为接口请求参数字符串类型
            String s = stringBuilder.toString();
            if(!"".equals(s)){
                body = s;
//                body = Tools.getDecrypt(privateKey,s);

                JSONObject jsonEncrypt = JSONObject.parseObject(body);
                String encrypt = jsonEncrypt.getString("encrypt");
                if(!StringUtils.isEmpty(encrypt)){
                    DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY, Constant.CORP_ID);
                    // 从post请求的body中获取回调信息的加密数据进行解密处理
                    logger.info("解密前报文body-length："+body.length());
                    String signature = jsonEncrypt.getString("msg_signature");
                    String timestamp = jsonEncrypt.getString("timeStamp");
                    String nonce = jsonEncrypt.getString("nonce");
                    String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encrypt);
                    logger.info("解密后报文plainText-length："+plainText.length());
                    body = plainText;
                }

                JSONObject json = JSONObject.parseObject(body);
                String token = json.getString("token");
                String opname = json.getString("opname");
                String sername = json.getString("sername");

                servletRequest.setAttribute("opname",opname);
                servletRequest.setAttribute("sername",sername);
                servletRequest.setAttribute("token",token);
                body = json.toJSONString();
            }

            //上传获取token
            String value = servletRequest.getParameter("params");
            if(!Tools.isNull(value)){
                DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY, Constant.CORP_ID);
                // 从post请求的body中获取回调信息的加密数据进行解密处理
//                logger.info("===解密前报文："+value);
                JSONObject jsonEncrypt = JSONObject.parseObject(value);
                String encrypt = jsonEncrypt.getString("encrypt");
                if(!StringUtils.isEmpty(encrypt)) {
                    String signature = jsonEncrypt.getString("msg_signature");
                    String timestamp = jsonEncrypt.getString("timeStamp");
                    String nonce = jsonEncrypt.getString("nonce");
                    String plainText = dingTalkEncryptor.getDecryptMsg(signature, timestamp, nonce, encrypt);
//                    logger.info("====解密后报文："+plainText);
                    this.addParameter("params", plainText);
                    JSONObject json = JSONObject.parseObject(plainText);
                    String token = json.getString("token");
                    String opname = json.getString("opname");
                    String sername = json.getString("sername");

                    servletRequest.setAttribute("opname",opname);
                    servletRequest.setAttribute("sername",sername);
                    servletRequest.setAttribute("token",token);

                    if (!Tools.isNull(token)) {
                        servletRequest.setAttribute("token", token);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("获取body参数异常", ex);
        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    @Override
    public ServletInputStream getInputStream() throws IOException {
        final ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(body.getBytes());
        ServletInputStream servletInputStream = new ServletInputStream() {
            @Override
            public boolean isFinished() {
                return false;
            }
            @Override
            public boolean isReady() {
                return false;
            }
            @Override
            public void setReadListener(ReadListener readListener) {
            }
            @Override
            public int read() throws IOException {
                return byteArrayInputStream.read();
            }
        };
        return servletInputStream;

    }
    public static void main(String[] args) throws DingTalkEncryptException {
        DingTalkEncryptor dingTalkEncryptor = new DingTalkEncryptor(Constant.TOKEN, Constant.ENCODING_AES_KEY,
                Constant.CORP_ID);
        String resultNeedEncrypt = "this is a apple";
        Map<String, Object> map = new HashMap<>();
        map.put("tranNo", "2140");
        map.put("vchno", "LD1721400075-1");
        map.put("latentId", "6c8d62c5-d523-48");
        map.put("token", "1668861944398090240");
        String mapStr = JSON.toJSONString(map);
        long currentTime = System.currentTimeMillis();
        String nonce = Utils.getRandomStr(8);
        System.out.println("加密前参数+++++++++jsonStr:"+mapStr+"++currentTime："+currentTime+"++nonce:"+nonce);
        Map<String, String> resultMap = dingTalkEncryptor.getEncryptedMap(mapStr, currentTime, nonce);
        System.out.println("加密后map+++++++++++"+resultMap.toString());
        String encrypt = resultMap.get("encrypt");
        String timeStamp = resultMap.get("timeStamp");
        String signature = resultMap.get("msg_signature");
        String plainText = dingTalkEncryptor.getDecryptMsg(signature, timeStamp, nonce, encrypt);
        System.out.println("解密结果String："+plainText);
        JSONObject obj = JSON.parseObject(plainText);
        System.out.println("解密结果obj："+obj);
    }
//
//    @Override
//    public String getParameter(String parameter) {
//        String value = super.getParameter(parameter);
//        if (value == null) {
//            return null;
//        }
//        return Tools.getDecrypt(privateKey,value);
//    }
//
//    @Override
//    public String[] getParameterValues(String parameter) {
//        String[] values = super.getParameterValues(parameter);
//        if (values == null) {
//            return null;
//        }
//        int count = values.length;
//        String[] encodedValues = new String[count];
//        for (int i = 0; i < count; i++) {
//            String value = values[i];
//
//            //AES解密数据
//            value = Tools.getDecrypt(privateKey,value);
//            encodedValues[i] = value;
//        }
//        return encodedValues;
//    }
//
//    @Override
//    public Map<String, String[]> getParameterMap(){
//        Map<String, String[]> values=super.getParameterMap();
//        if (values == null) {
//            return null;
//        }
//        Map<String, String[]> result=new HashMap<>();
//        for(String key:values.keySet()){
//            String encodedKey=key;
//            int count=values.get(key).length;
//            String[] encodedValues = new String[count];
//            for (int i = 0; i < count; i++){
//                String value = values.get(key)[i];
//                //AES解密数据
//                encodedValues[i] = Tools.getDecrypt(privateKey,value);
//            }
//            result.put(encodedKey,encodedValues);
//        }
//        return result;
//    }




    @Override
    public String getParameter(String name) {
        String[] values = params.get(name);
        if (values == null || values.length == 0) {
            return null;
        }
        return values[0];
    }

    @Override
    public String[] getParameterValues(String name) {
        return params.get(name);
    }

    public void addAllParameters(Map<String, Object> otherParams) {
        for (Map.Entry<String, Object> entry : otherParams.entrySet()) {
            addParameter(entry.getKey(), entry.getValue());
        }
    }

    public void addParameter(String name, Object value) {
        if (value != null) {
            if (value instanceof String[]) {
                params.put(name, (String[]) value);
            } else if (value instanceof String) {
                params.put(name, new String[] { (String) value });
            } else {
                params.put(name, new String[] { String.valueOf(value) });
            }
        }
    }

}


