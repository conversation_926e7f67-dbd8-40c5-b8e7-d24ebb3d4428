package com.tfrunning.gw.config.filter;

/**
 * 项目中的常量定义类
 */
public class Constant {
    /**
     * 企业corpid, 需要修改成开发者所在企业
     */
    public static final String CORP_ID = "ding18d658707db2302e35c2f4657eb6378f";
    /**
     * 应用的AppKey，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String APPKEY = "ding3e85wp7w6jhophmm";
    /**
     * 应用的AppSecret，登录开发者后台，点击应用管理，进入应用详情可见
     */
    public static final String APPSECRET = "-DXEb45qRc1X2pPuXHfrv96tp93_3UX0o3fMf8VMpB-5j71oQEEg5cTfX41Nauzh";

    /**
     * 数据加密密钥。用于回调数据的加密，长度固定为43个字符，从a-z, A-Z, 0-9共62个字符中选取,您可以随机生成
     */
    public static final String ENCODING_AES_KEY = "NiCwpd63TWKseM3Gf6HUWa7IrhiBj4o8jaSNkFpO7cO";

    /**
     * 加解密需要用到的token，企业可以随机填写。如 "12345"
     */
    public static final String TOKEN = "123654";

    /**
     * 应用的agentdId，登录开发者后台可查看
     */
    public static final Long AGENTID = Long.parseLong("2526764825");

    /**
     * 审批模板唯一标识，可以在审批管理后台找到
     */
    public static final String PROCESS_CODE = "PROC-224FF4D6-F78A-44F0-B96E-8A50F0E9CE0E";

    /**
     * 回调host
     */
    public static final String CALLBACK_URL_HOST = "***";

}
