package com.tfrunning.gw.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

@Configuration
public class JedisConfig {

    private static Logger logger = LoggerFactory.getLogger(JedisConfig.class);

    @Autowired
    private Environment environment;

    @Bean
    public JedisPool jedisPool() {

        JedisPoolConfig jedisPoolConfig = new JedisPoolConfig();
        jedisPoolConfig.setMaxIdle(Integer.parseInt(environment.getProperty("spring.redis.jedis.pool.max-idle")));
        jedisPoolConfig.setMinIdle(Integer.parseInt(environment.getProperty("spring.redis.jedis.pool.min-idle")));
        jedisPoolConfig.setMaxWaitMillis(Long.parseLong(environment.getProperty("spring.redis.jedis.pool.max-wait")));

        return new JedisPool(
                jedisPoolConfig,
                environment.getProperty("spring.redis.host"),
                Integer.parseInt(environment.getProperty("spring.redis.port")),
                30000,
                environment.getProperty("spring.redis.password")
        );
    }
}
