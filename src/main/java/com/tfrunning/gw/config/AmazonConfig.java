package com.tfrunning.gw.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;

@Configuration
public class AmazonConfig {
	private static Logger logger = LoggerFactory.getLogger(AmazonConfig.class);

	@Value("${media.aws.usekey}")
	private String usekey;

	@Value("${media.aws.access_key_id}")
	private String awsId;
	@Value("${media.aws.secret_access_key}")
	private String awsKey;
	@Value("${media.s3.region}")
	private String region;

	@Bean
	public S3Client s3client() {
		S3Client s3Client = null;
		logger.info("usekey is:" + usekey);
		if ("true".equals(usekey)) {
			logger.info("use s3 密钥策略---------------s3client---------------");
			AwsBasicCredentials awsCreds = AwsBasicCredentials.create(awsId, awsKey);
			s3Client = S3Client.builder().region(Region.of(region))
					.credentialsProvider(StaticCredentialsProvider.create(awsCreds)).build();
		} else {
			logger.info("use s3 服务器策略---------------s3client---------------");
			s3Client = S3Client.builder().region(Region.of(region)).build();
		}
		return s3Client;
	}

	@Bean
	public S3Presigner s3Presigner() {
		S3Presigner presigner = null;
		logger.info("usekey is:" + usekey);
		if ("true".equals(usekey)) {
			logger.info("use s3 密钥策略-----------------s3Presigner-------------");
			AwsBasicCredentials awsCreds = AwsBasicCredentials.create(awsId, awsKey);
			presigner = S3Presigner.builder().region(Region.of(region))
					.credentialsProvider(StaticCredentialsProvider.create(awsCreds)).build();
		} else {
			logger.info("use s3 服务器策略---------------s3Presigner---------------");
			presigner = S3Presigner.builder().region(Region.of(region)).build();
		}
		return presigner;
	}

}
