package com.tfrunning.gw.utils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;

/**
 * encodeURIComponent 转义操作
 */
public class EncodingUtil {

    /**
     * 编码
     * @param s
     * @return
     */
    public static String encodeURIComponent(String s) {
        String result = null;
        try {
            result = URLEncoder.encode(s, "UTF-8")
                    .replaceAll("\\+", "%20")
                    .replaceAll("\\%21", "!")
                    .replaceAll("\\%27", "'")
                    .replaceAll("\\%28", "(")
                    .replaceAll("\\%29", ")")
                    .replaceAll("\\%7E", "~");
        }catch (UnsupportedEncodingException e) {
           e.printStackTrace();
        }
        return result;
    }

    /**
     * 解码
     * @param s
     * @return
     */
    public static String decodeURIComponent(String s) {
        String result = null;
        try {
            result = URLDecoder.decode(s, "UTF-8");
        }catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        return result;
    }

}