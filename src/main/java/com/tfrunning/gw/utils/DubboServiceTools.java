package com.tfrunning.gw.utils;

import com.tfrunning.gw.SpringBeanUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import tf.mcs.service.IIfmsService;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

/**
 * Mcs连接中心
 * */

@Service
public class DubboServiceTools {

    @Autowired
    Environment environment;

    private static Logger logger = LoggerFactory.getLogger(DubboServiceTools.class);
    // systemId
    private final String SYSTEM_ID = "app";

    public RespTfData getResult (String serviceId, ReqTfData reqdata) {
        RespTfData respdata = new RespTfData();
        try {
            IIfmsService ifmsService=(IIfmsService) SpringBeanUtil.getBean("IIfmsService");
            reqdata.setSystemId(SYSTEM_ID);
            reqdata.setServiceId(serviceId);
            //logger.info(String.format("【REQUEST】发送请求参数：%s", reqdata.toString()));
            respdata=ifmsService.uniformCallMethod(reqdata.encrypt(environment.getProperty("custom.publickey")));
            //logger.info(String.format("【RESPONSE】dubbo通讯返回%s-%s",respdata.getRespDesc(),respdata));
        } catch(Exception e) {
            logger.error("Exception", e);
            return respdata;
        } finally {
            return respdata;
        }
    }

    public RespTfData getBase64Result (String serviceId, ReqTfData reqdata) {
        RespTfData respdata = new RespTfData();
        try {
            IIfmsService ifmsService=(IIfmsService)SpringBeanUtil.getBean("IIfmsService");
            reqdata.setSystemId(SYSTEM_ID);
            reqdata.setServiceId(serviceId);
//            logger.info("【REQUEST】【{}】", reqdata);
            respdata=ifmsService.dealmedia(reqdata);
            //logger.info(String.format("【RESPONSE】dubbo通讯返回%s-%s",respdata.getRespDesc(),respdata));
        } catch(Exception e) {
            logger.error("Exception", e);
            return respdata;
        } finally {
            return respdata;
        }
    }
    /**
     * 判空
     * */
    private boolean isNull(String str){
        return str==null||"".equals(str)||"null".equals(str)?true:false;
    }


}
