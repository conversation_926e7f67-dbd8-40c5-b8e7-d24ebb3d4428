package com.tfrunning.gw.utils;

/**
 * <p>BRC加解密处理包-3DES处理方法</p>
 * <p>Description:
 *    功能包括：
 *    1:DES的加解密
 *    2:
 *    3:
 * </p>
 *
 * <p>Copyright: 同方软银 刘宇 Copyright (c) 2013</p>
 * <p>Company: tfrunning </p>
 * @author: 刘宇
 * @date:2013-05-28
 * @version 1.0
 * @history: 1.0
 *
 */

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.SecretKeyFactory;
import javax.crypto.spec.DESKeySpec;
import java.math.BigDecimal;
import java.util.Random;

@Service
public class DESEncrypt {

	private static Logger logger = LoggerFactory.getLogger(DESEncrypt.class);

	public static byte[] encrypt(byte[] b_src) {
		return encrypt(b_src, 1);
	}

	public static byte[] decrypt(byte[] b_src) {
		return encrypt(b_src, 2);
	}


	public static String encrypt(String s) {
		StringBuffer sb = new StringBuffer(1024);
		byte[] b = encrypt(s.getBytes(), 1);
		if ((b == null) || (b.length < 1))
			return null;
		Random r = new Random(s.length());

		for (int i = 0; i < b.length; ++i) {
			char c = (char) (r.nextInt(10) + 71);
			sb.append(c);
			if (b[i] < 0) {
				c = (char) (r.nextInt(10) + 81);
				b[i] = (byte) (-b[i]);
				sb.append(c);
			}
			sb.append(Integer.toString(b[i], 16).toUpperCase());
		}
		sb.deleteCharAt(0);
		return sb.toString();
	}

	public static String decrypt(String s) {
		if (s.length() < 1) {
			return null;
		}

		String[] sByte = s.split("[G-Pg-p]");
		byte[] b = new byte[sByte.length];

		for (int i = 0; i < b.length; ++i) {
			char c = sByte[i].charAt(0);
			if (((c >= 'Q') && (c <= 'Z')) || ((c >= 'q') && (c <= 'z')))
				b[i] = (byte) (-Byte.parseByte(sByte[i].substring(1), 16));
			else
				b[i] = Byte.parseByte(sByte[i], 16);
		}

		byte[] ch = encrypt(b, 2);
		if ((ch == null) || (ch.length < 1))
			return null;

		return new String(ch);
	}

	private static byte[] encrypt(byte[] s, int mode) {
		byte[] ciphertext;
		byte[] salt = { -57, 115, 33, -116, 126, -56, -18, -103 };
		try {
			SecretKeyFactory keyFac = SecretKeyFactory.getInstance("DES");
			DESKeySpec desKeySpec = new DESKeySpec(salt);
			SecretKey desKey = keyFac.generateSecret(desKeySpec);

			Cipher cipher = Cipher.getInstance("DES/ECB/PKCS5Padding");

			cipher.init(mode, desKey);

			ciphertext = cipher.doFinal(s);
		} catch (Exception e) {
			// ARE.getLog().debug("DES Ecrypt error", e);

			logger.error("\n DES Ecrypt error", e);
			ciphertext = null;
		}
		return ciphertext;
	}

	public static void main(String[] args) throws Exception {

		String tocurrsum= "0.03";
		if (tocurrsum != null && !tocurrsum.isEmpty() && !tocurrsum.equals("null")) {
			long paysum = new BigDecimal(Double.parseDouble(tocurrsum)).multiply(new BigDecimal(100l)).longValue();
			System.out.println(paysum);
		}

		byte[] salt = { -57, 115, 33, -116, 126, -56, -18, -103 };
//		System.out.print(new String(salt, "utf-8"));
//		System.out.println();
//		System.out.println(DESEncrypt.encrypt("scm"));
		System.out.println(DESEncrypt.decrypt("4G5FJQ1CMW12N35O8H6KCJ21P13H5CO16L63G14MT21JY27"));
//		if (args.length > 0) {
//			String password = args[0];
//			System.out.println(password + "加密后" + DESEncrypt.encrypt(password));
//		}
	}
}
