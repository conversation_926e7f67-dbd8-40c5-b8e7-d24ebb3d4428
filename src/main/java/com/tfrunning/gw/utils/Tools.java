package com.tfrunning.gw.utils;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import org.apache.commons.io.FileUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.tomcat.util.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;
import tf.mcs.service.secu.SecurityTestAll;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URL;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service
public class Tools {

    private static Logger logger = LoggerFactory.getLogger(Tools.class);

    /**
     * Bankid
     * */
    public static String BANKID = "100000";

    public static int EXPIRE_SECOND = 60*15;//图片信息失效时间为15分钟
    public static int EXPIRE_PAD_TOKEN = 60*30;//pad登录token失效时间为30分钟
    public static int EXPIRE_WEB_TOKEN = 60*60*24*2;//登录token失效时间为2天

    /**
     * 是否为空
     * */
    public static Boolean isNull(String str){
        return str==null||"".equals(str)||"null".equals(str);
    }

    /**
     * 获取32位随机数
     *
     * */
    public synchronized static String get32String(){
        return UUID.randomUUID().toString().trim().replaceAll("-", "");
    }

    /**
     * 获取日期 时间
     *
     * */
    public static String getNowDateTime(){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd hh:mm:ss");
        return sdf.format(new Date());
    }

    /**
     * 获取代理定义
     * */
    public static RestTemplate getRestTemplate(){
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        requestFactory.setReadTimeout(10000);
        requestFactory.setConnectTimeout(10000);
//        Proxy proxy = new Proxy(Proxy.Type.HTTP, new InetSocketAddress(host, port));
//        requestFactory.setProxy(proxy);
        RestTemplate restTemplate = new RestTemplate(requestFactory); // 在这里进行尝试
        return restTemplate;
    }

    /**
     * 将图片转换成Base64编码
     */
    public static String getImgStr(String imgFile) {

        InputStream in = null;
        byte[] data = null;
        //读取图片字节数组
        try {
            in = new FileInputStream(imgFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            logger.info(String.format("图片转化base64异常",e.toString()));
        }
        return new String(Base64.encodeBase64(data));
    }

    public static String getVideoStr(String videoPath) {
        String result = "";

        try (InputStream inputStream =  FileUtils.openInputStream(new File(videoPath));){
            byte[] data = new byte[inputStream.available()];
            inputStream.read(data);
            result = new String(Base64.encodeBase64(data));
        } catch (IOException e) {
            logger.info(String.format("视频转化base64异常",e.toString()));
        }
        return result;
    }

    /***
     * PDF文件转PNG图片，全部页数
     *
     */

    public static ArrayList pdf2png(String filepath, String filename, String imgPath) {
        // 将pdf装图片 并且自定义图片得格式大小
        File dir = new File(filepath);//文件夹
        File file = new File(dir, filename);//PDF文件
        String general = filename.substring(0,filename.indexOf("."));//文件名（除去.pdf）
        String date = filepath.substring(filepath.lastIndexOf("/"));

        File imgDir = new File(dir,imgPath);
        if(imgDir.exists()) {
            deleteDir(imgDir);
        }
        imgDir.mkdir();
        ArrayList arr = new ArrayList();

        try {

            PDDocument doc = PDDocument.load(file);
            PDFRenderer renderer = new PDFRenderer(doc);
            int pageCount = doc.getNumberOfPages();
            String name;
            for (int i = 0; i < pageCount; i++) {
                BufferedImage image = renderer.renderImageWithDPI(i, 96); // Windows native DPI
                name = date+"/"+imgPath+"/"+general+"-"+i+".png";
                logger.info(String.format("第%d个文件路径为：%s",i,name));
                ImageIO.write(image, "PNG", new File(imgDir,general+"-"+i+".png"));
                arr.add(name);
            }
        } catch (IOException e) {
            logger.info(String.format("pdf转换成图片异常",e.toString()));
        }

        return arr;
    }


    /**
     * 删除文件夹以及文件夹中内容
     */
    public static boolean deleteDir(File dir) {
        if (dir.isDirectory()) {
            String[] children = dir.list();

            //递归删除目录中的子目录下
            for (int i=0; i<children.length; i++) {
                logger.info("===========待删除文件夹名=========="+children[i]);
                boolean success = deleteDir(new File(dir, children[i]));
                if(success){
                    logger.info("删除成功");
                }else{
                    logger.info("删除失败");
                }
            }
        }
        // 目录此时为空，可以删除
        return dir.delete();
    }

    /**
     * 循环压入参数
     */
    public static ReqTfData dealWithData(ReqTfData reqdata, HashMap hMap) {
        if(hMap != null && hMap.size() > 0){
            logger.info(String.format("传参：%s",hMap.toString()));

            Iterator<Map.Entry<String, String>> iterator = hMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, String> entry = iterator.next();
                reqdata.setReqEntity(entry.getKey(), entry.getValue());
            }
        }else{
            logger.info("传入map为空");
        }

        return reqdata;
    }

    /**
     * 向前台发送出具
     */
    public static void sentData(HttpServletResponse response, RespTfData respTfData) {
        OutputStream stream = null;
        try {
            Gson gson = new GsonBuilder().serializeNulls().create();
            stream = response.getOutputStream();
            stream.write(gson.toJson(respTfData).getBytes());
            stream.flush();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (stream != null) {
                try {
                    stream.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 签名算法
     * */
    public static String sign(List<String> values) {
        if (values == null) {
            throw new NullPointerException("values is null");
        }
        values.removeAll(Collections.singleton(null));// remove null

        Collections.sort(values);

        StringBuilder sb = new StringBuilder();
        for (String s : values) {
            sb.append(s);
        }
        return Hashing.sha1().hashString(sb, Charsets.UTF_8).toString();
    }

    public static String SHA1(String decript) {
        try {
            MessageDigest digest = java.security.MessageDigest
                    .getInstance("SHA-1");
            digest.update(decript.getBytes());
            byte messageDigest[] = digest.digest();
            // Create Hex String
            StringBuffer hexString = new StringBuffer();
            // 字节数组转换为 十六进制 数
            for (int i = 0; i < messageDigest.length; i++) {
                String shaHex = Integer.toHexString(messageDigest[i] & 0xFF);
                if (shaHex.length() < 2) {
                    hexString.append(0);
                }
                hexString.append(shaHex);
            }
            return hexString.toString();

        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return "";
    }

    public static String hideTel(String tel) {
        return tel.substring(0,3)+"****"+tel.substring(tel.length()-4);
    }

    /**
     * 获取IP地址
     */
    public static String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (!StringUtils.isEmpty(ip) && !"unKnown".equalsIgnoreCase(ip)) {

            //多次反向代理后会有多个ip值，第一个ip才是真实ip
            int index = ip.indexOf(",");
            if (index != -1) {
                return ip.substring(0, index);
            } else {
                return ip;
            }
        }
        ip = request.getHeader("X-Real-IP");
        if (!StringUtils.isEmpty(ip) && !"unKnown".equalsIgnoreCase(ip)) {
            return ip;
        }
        return request.getRemoteAddr();

    }

    @Value("${encrypt.publicKey}")
    private String pubkey;

    public String multiDecrypt(byte[] data) throws IOException {
        String sm4Key = SecurityTestAll.generateSM4Key();
        String cipherText = SecurityTestAll.SM4EncForECB_byte(sm4Key,data);

        String SM2Enc1 = SecurityTestAll.SM2Enc(pubkey, sm4Key);
        String SM2Enc2 = SecurityTestAll.SM2Enc(pubkey, sm4Key);
        String SM2Enc3 = SecurityTestAll.SM2Enc(pubkey, sm4Key);

        cipherText = cipherText.substring(0,12)+SM2Enc1+SM2Enc2+SM2Enc3+cipherText.substring(12);

        return cipherText;

    }

    public static String getDecrypt(String privateKey,String s) {
        String body = "";
        try {
            long start = System.currentTimeMillis();
            logger.info("开始解密时间："+start);

            String serid = s.substring(32, 32 + 256*3);
            String content = s.substring(0, 32) + s.substring(32 + 256*3);
            String decryptAesKey = "",keyByPK;

            for(int j= 0; j <3; j++ ){
                keyByPK = serid.substring(j*256,(j+1)*256);

                    decryptAesKey = SecurityTestAll.SM2Dec(privateKey, keyByPK);

                if(!Tools.isNull(decryptAesKey)){
                    body = SecurityTestAll.SM4DecForECB(decryptAesKey, content);
                    if(!Tools.isNull(body)){
                        break;
                    }
                }
            }

            long end = System.currentTimeMillis();

            logger.info("结束解密时间："+end);
            logger.info("解密时间："+(end-start)/1000);
            return body;
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }

    }
    public static boolean downLoadFile(String url,String filePath) {
        try {

            URL uri = new URL(url);
            InputStream in = uri.openStream();
            File file=new File(filePath);
            if(file.exists()){
                file.delete();
            }
            FileOutputStream fo = new FileOutputStream(file.getPath());
            byte[] buf = new byte[1024];
            int length = 0;
            logger.info("开始下载:" + url);
            while ((length = in.read(buf, 0, buf.length)) != -1) {
                fo.write(buf, 0, length);
            }
            in.close();
            fo.close();
            logger.info(url + "下载完成");
            return true;
        } catch (Exception e) {
            logger.info("下载失败:"+url+","+e.toString());
            return false;
        }
    }
}
