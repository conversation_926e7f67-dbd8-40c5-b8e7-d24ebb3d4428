package com.tfrunning.gw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;

import java.util.concurrent.TimeUnit;

@Service
public class RedisTools {

    private static Logger logger = LoggerFactory.getLogger(RedisTools.class);

    @Value("${spring.redis.database}")
    private Integer REDIS_DATABASE;

    @Autowired
    private JedisPool jedisPool;


    /**
     * <p>
     * 判断key是否存在
     * </p>
     *
     * @param key
     * @return true OR false
     */
    public Boolean exists(String key) {
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            result = jedis.exists(key);
        } catch (Exception e) {
            result = false;
        } finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    /**
     * 指定缓存失效时间
     * @param key 键
     * @param time 时间(秒)
     * @return
     */
    public boolean expire(String key,int time){
        Jedis jedis = null;
        boolean result = false;
        try {
            if(time>0){
                jedis = jedisPool.getResource();
                jedis.select(REDIS_DATABASE);
                jedis.expire(key,time);
            }
            result =  true;
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    //============================String=============================
    /**
     * 普通缓存放入并设置时间
     * @param key 键
     * @param value 值
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public boolean set(String key,String value,int time){
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.set(key,value);
            jedis.expire(key,time);
            result = true;
            logger.info(String.format("Redis保存key:%s，有效时间：%d秒，值value：%s",key,time,value));
        } catch (Exception e) {
            logger.error("redis保存数据异常", e);
            result = false;
        } finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    public boolean set(String key,String value){
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.set(key,value);
            result = true;
            logger.info(String.format("Redis保存key:%s",key));
        } catch (Exception e) {
            logger.error("redis保存数据异常", e);
            result = false;
        } finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    /**
     * 普通缓存获取
     * @param key 键
     * @return 值
     */
    public String get(String key){
        Jedis jedis = null;
        String value = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            value = jedis.get(key);
            logger.info(String.format("Redis获取key:%s，value：%s",key,value));
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
            return value;
        }
    }

    /**
     * 删除key
     * @param key 键
     * @return 值
     */
    public void del(String key){
        Jedis jedis = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.del(key);
            logger.info(String.format("Redis删除key:%s",key));
        } catch (Exception e) {
            logger.error(String.format("Redis删除key发生异常%s",e));
        } finally {
            if(jedis != null) jedis.close();
        }
    }

    //================================Map=================================
    /**
     * HSET key field value
     * 将哈希表 key 中的域 field 的值设为 value 。
     * 如果 key 不存在，一个新的哈希表被创建并进行 HSET 操作。
     * 如果域 field 已经存在于哈希表中，旧值将被覆盖。
     * <p/>
     * HMSET key field value [field value ...]
     * 同时将多个 field-value (域-值)对设置到哈希表 key 中。
     * 此命令会覆盖哈希表中已存在的域。
     * 如果 key 不存在，一个空哈希表被创建并执行 HMSET 操作。
     */
    public boolean hset(String key,String field,String value){
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.hset(key,field,value);
            result = true;
            logger.info(String.format("Redis保存key:%s，域field：%s，值value:%s",key,field,value));
        } catch (Exception e) {
            logger.error("redis保存数据异常", e);
            result = false;
        } finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    /**
     * @param time 时间(秒) time要大于0 如果time小于等于0 将设置无限期
     */
    public boolean hset(String key,String field,String value, int time){
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.hset(key,field,value);
            jedis.expire(key,time);
            result = true;
            logger.info(String.format("Redis保存key:%s，域field：%s，有效时间：%d秒，值value：%s",key,field,time,value));
        } catch (Exception e) {
            logger.error("redis保存数据异常", e);
            result = false;
        } finally {
            if (jedis != null) jedis.close();
            return result;
        }
    }

    /**
     * HGET key field
     * 返回哈希表 key 中给定域 field 的值。
     * <p/>
     * HMGET key field [field ...]
     * 返回哈希表 key 中，一个或多个给定域的值。
     * 如果给定的域不存在于哈希表，那么返回一个 nil 值。
     * 因为不存在的 key 被当作一个空哈希表来处理，所以对一个不存在的 key 进行 HMGET 操作将返回一个只带有 nil 值的表。
     * <p/>
     * HGETALL key
     * 返回哈希表 key 中，所有的域和值。
     * 在返回值里，紧跟每个域名(field name)之后是域的值(value)，所以返回值的长度是哈希表大小的两倍。
     */
    public String hget(String key,String field){
        Jedis jedis = null;
        String value = null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            value = jedis.hget(key,field);
            logger.info(String.format("Redis获取key:%s，域field:%s，value：%s",key,field,value));
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
            return value;
        }
    }

    /**
     * <p>
     * 通过key和field判断是否有指定的value存在
     * </p>
     *
     * @param key
     * @param field
     * @return
     */
    public Boolean hexists(String key, String field) {
        Jedis jedis = null;
        Boolean res = false;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            res = jedis.hexists(key, field);
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
            return res;
        }
    }

    /**
     * 设置setNX
     * @param key
     * @param timeout	过期时间（秒）
     * @return
     */
    public boolean setNX(String key, int timeout) {
        Jedis jedis = null;
        Long res=null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            res = jedis.setnx(key,"1");
            if(res==1)
            {
                jedis.expire(key,timeout);
            }
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
            return res==1?true:false;
        }
    }

    /**
     * 设置key对应value(数字类型)每次incr调用递增1  --  可以为负数
     */
    public void incr(String key){
        Jedis jedis = null;
        Long res=null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.incr(key);
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
        }
    }


    /**
     *设置key对应value(数字类型)每次decr调用递减1  --  可以为负数
     */
    public void decr(String key){
        Jedis jedis = null;
        Long res=null;
        try {
            jedis = jedisPool.getResource();
            jedis.select(REDIS_DATABASE);
            jedis.decr(key);
        } catch (Exception e) {
            logger.error("redis获取数据异常", e);
        } finally {
            if (jedis != null) jedis.close();
        }
    }

}
