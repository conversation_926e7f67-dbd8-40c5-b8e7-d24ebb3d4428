package com.tfrunning.gw.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Random;

public class GUIDGenerator {
	public static void main(String[] args) {
		System.out.println(getTxNo27());
	}

	private static final SimpleDateFormat sdf_millisecond2 = new SimpleDateFormat("yyyyMMddHHmmssSSS");
	private static final SimpleDateFormat sdf_millisecond = new SimpleDateFormat("yyyyMMddHHmmss");


	private static Random random = new Random();
	private static final char[] CHARACTER_TABLE = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c',
			'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x',
			'y', 'z', 'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S',
			'T', 'U', 'V', 'W', 'X', 'Y', 'Z' };

	public static String getTxNo27() {
		String timeString = sdf_millisecond2.format(Calendar.getInstance().getTime());
		return timeString + getRandomCharAndNumber(10, true);
	}

	private static String getRandomNumber(int length) {
		String string = String.valueOf(Math.abs(random.nextLong()));
		while (string.length() < length) {
			string = string + String.valueOf(Math.abs(random.nextLong()));
		}
		return string.substring(0, length);
	}

	private static String getRandomCharAndNumber(int length, boolean upperCaseSupported) {
		StringBuffer rsb = new StringBuffer();
		if (upperCaseSupported) {
			for (int i = 0; i < length; i++)
				rsb.append(CHARACTER_TABLE[random.nextInt(62)]);
		} else {
			for (int i = 0; i < length; i++) {
				rsb.append(CHARACTER_TABLE[random.nextInt(36)]);
			}
		}
		return rsb.toString();
	}

	public static String getTxNo32() {
		String timeString = sdf_millisecond.format(Calendar.getInstance().getTime());

		timeString="OPS"+timeString+"mx";

		return timeString + getRandomCharAndNumber(13, true);
	}

}
