package com.tfrunning.gw.utils;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
public class TokenTools {
	
	@Autowired
	private IdWorker idWorker;

	@Autowired
	private RedisTools redisTools;
	
	/**
	 * 颁发token令牌
	 * @param username
	 * @return
	 */
	@SuppressWarnings({ "finally", "rawtypes", "unchecked" })
	public String getToken(String username) {
		if(redisTools.exists(username)){
			String oldToken = redisTools.get(username);
			redisTools.del(username);
			redisTools.del(oldToken);
		}
		String newToken = String.valueOf(idWorker.nextId());
		redisTools.set(username,newToken,Tools.EXPIRE_PAD_TOKEN);
		redisTools.hset(newToken,"username",username,Tools.EXPIRE_PAD_TOKEN);
		return newToken;
	}
	
	/**
	 * 验证token令牌
	 * @param token
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "finally" })
	public String getUserid(String token) {
		String userid = null;
		if(redisTools.exists(token)){
			userid = redisTools.hget(token,"username");
		}
		return userid;
	}

	/**
	 * 验证token令牌并重置token时效
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "finally" })
	public Boolean checkTokenWithExpire(String token) {
		if(redisTools.exists(token)){
			String username = getUserid(token);
			redisTools.expire(token,Tools.EXPIRE_PAD_TOKEN);
			redisTools.expire(username,Tools.EXPIRE_PAD_TOKEN);
			return true;
		}else{
			return false;
		}

	}
	
	/**
	 * 验证token令牌并重置token时效
	 */
	@SuppressWarnings({ "finally" })
	public void cleanupToken(String token) {
		if(redisTools.exists(token)){
			String username = getUserid(token);
			redisTools.del(username);
			redisTools.del(token);
		}
	}

}
