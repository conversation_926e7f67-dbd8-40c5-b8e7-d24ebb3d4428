package com.tfrunning.gw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.listener.KeyExpirationEventMessageListener;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.stereotype.Component;

@Component
public class RedisKeyExpirationListener extends KeyExpirationEventMessageListener {

    private static Logger logger = LoggerFactory.getLogger(RedisKeyExpirationListener.class);

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    BaiduTools baiduTools;
    public RedisKeyExpirationListener(RedisMessageListenerContainer listenerContainer) {
        super(listenerContainer);
    }

    @Override
    public void onMessage(Message message, byte[] pattern) {
        String redisKey = message.toString();

        if("access_token".equals(redisKey)){
            logger.info("redis监听失效access_token，重新刷新！！！！！");
            weiChatTools.getToken();
        }
        if("baidu_access_token".equals(redisKey)){
            logger.info("redis监听失效baidu_access_token，重新刷新！！！！！");
            baiduTools.getAccessToken();
        }
    }
}
