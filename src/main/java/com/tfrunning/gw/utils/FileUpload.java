package com.tfrunning.gw.utils;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import sun.misc.BASE64Decoder;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;


/**
 * 上传服务
 *
 * <AUTHOR>
 * @version
 */
@Service
public class FileUpload {

    @Autowired
    IdWorker idWorker;

    @Autowired
    Environment environment;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private Logger log = LoggerFactory.getLogger(FileUpload.class);

    public RespTfData ngUpload(HttpServletRequest request, HttpServletResponse res) {

        log.info("开始执行");
        RespTfData respdata = new RespTfData();

        respdata.setRespCode("0000");

        ArrayList<HashMap> container = new ArrayList<HashMap>();

        // 解析器解析request的上下文
        CommonsMultipartResolver multipartResolver = new CommonsMultipartResolver(request.getSession().getServletContext());
        // 先判断request中是否包涵multipart类型的数据，
        if (multipartResolver.isMultipart(request)) {
            // 再将request中的数据转化成multipart类型的数据
            MultipartHttpServletRequest multiRequest = (MultipartHttpServletRequest) request;

            String params = multiRequest.getParameter("params");
            Map<String, Object> parmMap = JSON.parseObject(params, Map.class);
            String prefix = (String) parmMap.get("prefix");
            String applyno = (String) parmMap.get("applyno");

            if(Tools.isNull(prefix)){
                respdata.setRespCode("9999");
                respdata.setRespDesc("文件分类不能为空");
                return respdata;
            }

            if(Tools.isNull(applyno)){
                respdata.setRespCode("9999");
                respdata.setRespDesc("申请号不能为空");
                return respdata;
            }

            Iterator<String> iter = multiRequest.getFileNames();
            while (iter.hasNext()) {
                // 这里的name为fileItem的alias属性值，相当于form表单中name
                String name = iter.next();
                log.info("name为：【{"+ name +"}】");
                // 根据name值拿取文件
                MultipartFile file = multiRequest.getFile(name);
                if (file != null) {
                    String fileName = file.getOriginalFilename();
                    log.info("初始文件名为：【{"+ fileName +"}】");

                    //文件上传检查文件类型开关:0-关,1-开
                    String fileCheckSwitch = redisTemplate.opsForValue().get("fileCheckSwitch");

                    if("1".equals(fileCheckSwitch)){

                        if(null != fileName){
                            String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);

                            if(StringUtils.isBlank(suffix)){
                                respdata.setRespCode("9999");
                                respdata.setRespDesc("上传文件失败,文件没有格式后缀！");
                                return respdata;
                            }else{
                                Set<String> fileSuffixSet = redisTemplate.opsForSet().members("fileUploadSuffix");

                                if(null != fileSuffixSet && !fileSuffixSet.contains(suffix.toLowerCase())){
                                    respdata.setRespCode("9999");
                                    respdata.setRespDesc("上传文件失败,目前支持上传 " + fileSuffixSet + " 格式文件");
                                    return respdata;
                                }
                            }
                        }else{
                            respdata.setRespCode("9999");
                            respdata.setRespDesc("上传文件失败,文件名不能为空！");
                            return respdata;
                        }
                    }
                    int random = new Random().nextInt(999999);
                    int pointLocation = fileName.lastIndexOf(".");

                    // 修改文件名
                    String changeFileName = prefix.substring(0,1) + applyno + random + fileName.substring(pointLocation);
                    log.info("文件名更改为：【{"+ changeFileName +"}】");
                    String webappLocation = environment.getProperty("custom.uploadfilepath");
                    log.info("获得的路径地址为：【{"+ webappLocation +"}】");
                    String path = webappLocation + File.separator + changeFileName;
                    log.info("最终文件为：【{"+ path +"}】");
                    File localFile = new File(path);
                    if (!localFile.getParentFile().exists()) {
                        // 如果目标文件所在的目录不存在，则创建父目录
                        localFile.getParentFile().mkdirs();
                        log.info("parent:" + localFile.getParentFile().getPath());
                    }
                    // 写文件到本地
                    try {
                        file.transferTo(localFile);
                        log.info("文件(" + fileName + ")上传成功,文件位置及名称为：【{}】" + path);

                        HashMap<String, String> singleMap = new HashMap<>();
                        singleMap.put("fileName", changeFileName);
                        singleMap.put("filePath", path);//无需暴露内部路径
                        container.add(singleMap);
                    } catch (IOException e) {
                        // TODO Auto-generated catch block
                        log.error("写文件(" + fileName + ")到服务器异常", e);
                        respdata.setRespCode("7777");
                        respdata.setRespDesc("写文件" + fileName + "到服务器异常");
                        return respdata;
                    }
                }
            }
            respdata.setRespEntity("fileList",container);
            respdata.setRespDesc("上传文件成功");
            return respdata;
        } else {
            respdata.setRespDesc("request中不包涵multipart类型的数据");
            return respdata;
        }
    }

    /**
     * 上传文件到S3
     * 文件必须为base64格式,微信不支持File格式
     * @param requestMap
     * @return
     */
    public RespTfData uploadFile2S3(HashMap<String, Object> requestMap) {
        RespTfData respTfData = new RespTfData();
        ArrayList<HashMap<String, String>> container = new ArrayList<>();

        try{
            String pathName = (String) requestMap.get("pathName");
            String applyNo = (String) requestMap.get("applyno");
            List<Map<String, String>> base64FileList =
                    (List<Map<String, String>>) requestMap.get("base64FileList");

            if (!Tools.isNull(pathName)) {

                if (null != base64FileList) {

                    for (Map<String, String> base64FileMap : base64FileList) {
                        String fileName = base64FileMap.get("fileName");//文件名称
                        String base64File = base64FileMap.get("base64File");//base64文件字符串

                        if (!Tools.isNull(fileName)) {

                            if (!Tools.isNull(base64File)) {
                                String fileSuffix = fileName.substring(fileName.lastIndexOf(".") + 1);

                                if (!Tools.isNull(fileSuffix)) {
                                    //文件上传检查文件类型开关:0-关,1-开
                                    String fileCheckSwitch = redisTemplate.opsForValue().get("fileCheckSwitch");
                                    Set<String> fileSuffixSet = redisTemplate.opsForSet().members("fileUploadSuffix");

                                    if (null == fileSuffixSet
                                            || fileSuffixSet.contains(fileSuffix.toLowerCase())
                                            || !"1".equals(fileCheckSwitch)) {
                                        int random = new Random().nextInt(999999);
                                        //修改文件名
                                        String changeFileName;

                                        if(Tools.isNull(applyNo)){
                                            changeFileName = pathName.substring(0, 1) + applyNo
                                                    + random + fileName.substring(fileName.lastIndexOf("."));
                                        }else{
                                            changeFileName = pathName.substring(0, 1) + System.currentTimeMillis()
                                                    + random + fileName.substring(fileName.lastIndexOf("."));
                                        }
                                        String fileWebPath = environment.getProperty("custom.uploadfilepath")
                                                + File.separator + changeFileName;
                                        File localFile = new File(fileWebPath);

                                        //如果目标文件所在的目录不存在，则创建父目录
                                        if (!localFile.getParentFile().exists()) {
                                            localFile.getParentFile().mkdirs();
                                        }
                                        generateFileByBase64(base64File, fileWebPath);
                                        HashMap<String, String> singleMap = new HashMap<>();
                                        singleMap.put("fileName", changeFileName);
                                        singleMap.put("filePath", fileWebPath);
                                        container.add(singleMap);
                                    } else {
                                        respTfData.setRespCode("9999");
                                        respTfData.setRespDesc("上传文件失败,目前支持上传 " + fileSuffixSet + " 格式文件");
                                        return respTfData;
                                    }
                                } else {
                                    respTfData.setRespCode("9999");
                                    respTfData.setRespDesc("文件[" + fileName + "],文件没有格式后缀!");
                                    return respTfData;
                                }
                            } else {
                                respTfData.setRespCode("9999");
                                respTfData.setRespDesc("文件[" + fileName + "],未获取到文件信息");
                                return respTfData;
                            }
                        } else {
                            respTfData.setRespCode("9999");
                            respTfData.setRespDesc("文件名称不能为空");
                            return respTfData;
                        }
                    }
                } else {
                    respTfData.setRespCode("9999");
                    respTfData.setRespDesc("Base64文件列表不能为空");
                    return respTfData;
                }
            } else {
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("文件存放路劲名称不能为空");
                return respTfData;
            }
        }catch (Exception e){
            e.printStackTrace();
            log.info("H5-上传Base64图片服务器异常", e);
            respTfData.setRespCode("7777");
            respTfData.setRespDesc("上传文件成功");
            return respTfData;
        }
        respTfData.setRespCode("0000");
        respTfData.setRespEntity("fileList", container);
        respTfData.setRespDesc("上传文件成功");
        return respTfData;
    }

    /**
     * base64转换为文件
     * @param base64Str         base64文件字符串
     * @param filePath          存放文件路径
     * @return
     * @throws IOException
     */
    public void generateFileByBase64(String base64Str, String filePath) throws IOException {

        if (!Tools.isNull(base64Str)){

            try(FileOutputStream fileOutputStream = new FileOutputStream(filePath)) {
                BASE64Decoder base64Decoder = new BASE64Decoder();
                byte[] bs = base64Decoder.decodeBuffer(base64Str);
                fileOutputStream.write(bs);
            } catch (Exception e) {
                e.printStackTrace();
                log.error("base64转换为文件服务器异常", e);
                throw e;
            }
        }
    }
}