package com.tfrunning.gw.utils;

import com.alibaba.fastjson.JSONObject;
import com.tfrunning.gw.model.PushMessage;
import com.tfrunning.gw.model.TemplateData;
import com.tfrunning.gw.model.WechatTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

@Service
public class WeiChatTools {

    private Logger logger = LoggerFactory.getLogger(WeiChatTools.class);
    private static String ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid={appid}&secret={secret}";
    private static String JSAPI_TICKET = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token={token}&type=jsapi";
    private static String OPENID = "https://api.weixin.qq.com/sns/oauth2/access_token?appid={appid}&secret={secret}&code={code}&grant_type=authorization_code";
    public static String  CONTENT="https://api.weixin.qq.com/cgi-bin/message/template/send?access_token=ACCESS_TOKEN";
    public static String  SENDCONTENT="https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=ACCESS_TOKEN";
    public static String  QRCODE="https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=ACCESS_TOKEN";
    public static String  MENU = "https://api.weixin.qq.com/cgi-bin/menu/create?access_token=ACCESS_TOKEN";

    public static String  SHOWQRCODE = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=TICKET";
    public static String  USERINFO = "https://api.weixin.qq.com/cgi-bin/user/info?access_token=ACCESS_TOKEN&openid=OPENID&lang=zh_CN";


    public String getAPP_ID() {
        return APP_ID;
    }

    @Value("${weichat.app_id}")
    private String APP_ID;

    @Value("${weichat.secret_key}")
    private String APP_SECRET;

    @Autowired
    RedisTools redisTools;

    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    private IdWorker idWorker;

    /**
     * 获取openid
     * */
    public RespTfData getOpenid(String code){

        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("获取微信授权成功！");
        Map sigparam = new HashMap();

        sigparam.put("appid", APP_ID);
        sigparam.put("secret", APP_SECRET);
        sigparam.put("code", code);

        RestTemplate tfRestTemplate = Tools.getRestTemplate(); // 在这里进行尝试
        String sigRes = tfRestTemplate.getForObject(OPENID, String.class, sigparam);
        logger.info(String.format("微信授权返回结果:%s",sigRes));

        if(Tools.isNull(sigRes)){
            logger.info("微信授权返回空！！！！！");
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("微信授权失败！");
        }else{
            JSONObject result = JSONObject.parseObject(sigRes);
            String error = String.valueOf(result.get("errcode"));

            if(!Tools.isNull(error)){
                String errmsg = (String) result.get("errmsg");
                logger.info(String.format("微信授权返回错误信息:%s",errmsg));
                respTfData.setRespCode("9999");
                respTfData.setRespDesc(errmsg);
            }else{
                String openid = (String) result.get("openid");
                if(Tools.isNull(openid)){
                    logger.info("微信授权返回openid为空！！！！");
                    respTfData.setRespCode("9999");
                    respTfData.setRespDesc("微信授权返回openid为空");
                }else{
                    //生成token
                    String token = redisTools.hget(openid,"token");
                    if(Tools.isNull(token)){
                        token = String.valueOf(idWorker.nextId());
                    }
                    //以openid为key可避免数据冗余，并且一个openid只保证有一个token有效
                    redisTools.hset(openid,"token",token,Tools.EXPIRE_WEB_TOKEN);
                    logger.info(String.format("加密前openid:%s",openid));
                    openid = DESEncrypt.encrypt(openid);
                    logger.info(String.format("加密后openid:%s",openid));

                    respTfData.setRespEntity("token",token);
                    respTfData.setRespEntity("openid",openid);
                    respTfData.setRespCode("0000");
                    respTfData.setRespDesc("获取微信授权成功！");
                }
            }
        }

        return respTfData;

    }

    /**
     * 获取ACCESS_TOKEN
     * */
    public String getToken(){
        Map sigparam = new HashMap();

        sigparam.put("appid", APP_ID);
        sigparam.put("secret", APP_SECRET);

        String token = null;

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();
            Map sigRes = tfRestTemplate.getForObject(ACCESS_TOKEN, Map.class, sigparam);
            logger.info(String.format("获取微信access_token返回结果:%s",sigRes.toString()));
            Integer errcode = (Integer) sigRes.get("errcode");
            if(errcode!=null){
                logger.info(String.format("获取微信access_token返回错误信息：%s",(String)sigRes.get("errmsg")));
                return null;
            }

            String access_token = (String) sigRes.get("access_token");
            if(!Tools.isNull(access_token)){
                redisTools.set("access_token",access_token);
                // 获取JSAPI_TICKET
                getToken(access_token);
                token = access_token;
            }else{
                logger.info("返回微信access_token为空！！！！！！！");
            }
        }catch (Exception e){
            logger.info("获取微信access_token失败："+e.getMessage(),e);
        }
        return token;
    }

    /**
     * 获取JSAPI_TICKET
     * */
    private void getToken(String access){
        Map sigparam = new HashMap();

        sigparam.put("token", access);

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();
            Map sigRes = tfRestTemplate.getForObject(JSAPI_TICKET, Map.class, sigparam);
            logger.info(String.format("获取微信JSAPI_TICKET返回结果:%s",sigRes.toString()));
            Integer errcode = (Integer) sigRes.get("errcode");
            if(errcode!=0){
                logger.info(String.format("获取微信JSAPI_TICKET返回错误信息：%s",(String)sigRes.get("errmsg")));
                return;
            }

            String jsapi_ticket = (String) sigRes.get("ticket");
            if(!Tools.isNull(jsapi_ticket)){
                redisTools.set("jsapi_ticket",jsapi_ticket);
            }else{
                logger.info("返回微信jsapi_ticket为空！！！！！！！");
            }
        }catch (Exception e){
            logger.info("获取微信jsapi_ticket失败："+e.getMessage(),e);
        }
    }

    /**
     * 向公众号推送消息
     * */
    public void sendMsg(String openid,String template, String tempUrl, Map map){
        String access_token = redisTools.get("access_token");
        if(Tools.isNull(access_token)){
            saveMsg(openid,template,map,"{\"errmsg\":\"推送消息失败，获取access_token为空\",\"errcode\":\"9999\"}");
            logger.info(String.format("推送消息失败，获取access_token为空，openid：%s，推送内容：%s", openid, map.toString()));
            return;
        }

        RestTemplate tfRestTemplate = Tools.getRestTemplate();

        WechatTemplate wechatTemplate = new WechatTemplate();
        wechatTemplate.setTouser(openid);
        wechatTemplate.setTemplate_id(template);
        wechatTemplate.setTopcolor("#5B58F8");
        if(!Tools.isNull(tempUrl)){
            wechatTemplate.setUrl(tempUrl);
        }else{
            if(map.get("linkUrl")!=null){

                String orginUrl=(String)map.get("linkUrl");

                String lastUrl= null;
                try {
                    lastUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+APP_ID+"&redirect_uri="+ URLEncoder.encode(orginUrl,"UTF-8")+"&response_type=code&scope=snsapi_base#wechat_redirect";
                    logger.info("链接地址："+lastUrl);
                    wechatTemplate.setUrl(lastUrl);
                    map.remove("linkUrl");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }

            }
        }

        Map<String,TemplateData> data = new HashMap<String,TemplateData>();
        TemplateData templateData = null;

        Iterator<Map.Entry<String, String>> iterator = map.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, String> entry = iterator.next();
            templateData = new TemplateData();
            templateData.setValue(String.valueOf(entry.getValue()));
            data.put(entry.getKey(),templateData);
        }

        wechatTemplate.setData(data);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<WechatTemplate> entity = new HttpEntity<WechatTemplate>(wechatTemplate, headers);

        String url=CONTENT.replace("ACCESS_TOKEN", access_token);

        ResponseEntity<String> responseEntity = tfRestTemplate.postForEntity(url, entity, String.class);
        String backmsg = responseEntity.getBody();
        logger.info(String.format("推送消息返回内容：%s", backmsg));
        saveMsg(openid,template,map,backmsg);
    }

    public void sendNoticeMsg(String openid, Map map){
        String access_token = redisTools.get("access_token");
        if(Tools.isNull(access_token)){
            saveMsg(openid,null,map,"{\"errmsg\":\"推送消息失败，获取access_token为空\",\"errcode\":\"9999\"}");
            logger.info(String.format("推送消息失败，获取access_token为空，openid：%s，推送内容：%s", openid, map.toString()));
            return;
        }

        RestTemplate tfRestTemplate = Tools.getRestTemplate();

        PushMessage pushMessage = new PushMessage();
        pushMessage.setTouser(openid);
        pushMessage.setMsgtype("text");
        Map<String,Object> hashMap = new HashMap<>();
        hashMap.put("content",String.valueOf(map.get("content")));
        pushMessage.setText(hashMap);
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<PushMessage> entity = new HttpEntity<PushMessage>(pushMessage, headers);
        String url=SENDCONTENT.replace("ACCESS_TOKEN", access_token);
        ResponseEntity<String> responseEntity = tfRestTemplate.postForEntity(url, entity, String.class);
        String backmsg = responseEntity.getBody();
        logger.info(String.format("推送消息返回内容：%s", backmsg));
        saveMsg(openid,"预申请接收申请微信通知",map,backmsg);
    }


    /**
     * 创建二维码ticket
     * @param qrtype 1：机构；2：操作员；
     * @param code 机构编号/操作员编号
     * */
    public RespTfData getQRcode(String qrtype,String code){
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("创建二维成功");

        String access_token = redisTools.get("access_token");
        if(Tools.isNull(access_token)){
            logger.info(String.format("生成二维码，获取access_token为空"));
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取access_token为空");
            return respTfData;
        }

        RestTemplate tfRestTemplate = Tools.getRestTemplate();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        String sceneid=code;
        if(qrtype.equals("1")){
            sceneid="Branch"+sceneid;
        }
        String str = "{\"action_name\":\"QR_LIMIT_STR_SCENE\",\"action_info\":{\"scene\":{\"scene_str\":\""+ sceneid +"\"}}}";

        HttpEntity entity = new HttpEntity(str, headers);

        String url=QRCODE.replace("ACCESS_TOKEN", access_token);

        Map result = tfRestTemplate.postForObject(url, entity, Map.class);

        logger.info(String.format("生成二维码返回内容：%s",result.toString()));

        Integer errcode = (Integer) result.get("errcode");
        if(errcode != null){
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取二维码失败");
            return respTfData;
        }
        String qrUrl = (String) result.get("url");
        String ticket = (String) result.get("ticket");
        String imageUrl=SHOWQRCODE.replace("TICKET", ticket);

        logger.info(String.format("二维码地址：%s",qrUrl));
        logger.info(String.format("二维码图片地址：%s",imageUrl));

        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("sceneid", sceneid);
        reqdata.setReqEntity("qrtype", qrtype);
        reqdata.setReqEntity("code", code);
        reqdata.setReqEntity("url", qrUrl);
        reqdata.setReqEntity("ticket", ticket);
        reqdata.setReqEntity("imageUrl", imageUrl);
        dubboServiceTools.getResult("1205", reqdata);

        respTfData.setRespEntity("qr",imageUrl);
        return respTfData;
    }


    /**
     * 获取用户信息
     * @param openid
     * */
    public RespTfData getUserInfo(String openid){
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("获取用户信息成功");

        String access_token = redisTools.get("access_token");
        if(Tools.isNull(access_token)){
            logger.info(String.format("获取用户信息，获取access_token为空"));
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取access_token为空");
            return respTfData;
        }

        RestTemplate tfRestTemplate = Tools.getRestTemplate();

        String url=USERINFO.replace("ACCESS_TOKEN", access_token).replace("OPENID",openid);

        Map result = tfRestTemplate.getForObject(url,Map.class);

        logger.info(String.format("获取客户信息返回内容：%s",result.toString()));

        Integer errcode = (Integer) result.get("errcode");
        if(errcode != null){
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取客户信息失败");
            return respTfData;
        }

        String nickname = (String) result.get("nickname");
        respTfData.setRespEntity("nickname",nickname);
        return respTfData;
    }


    public void saveMsg(String openid,String template, Map map,String result){
        try {
            ReqTfData reqdata = new ReqTfData();
            reqdata.setReqEntity("bankid", Tools.BANKID);
            reqdata.setReqEntity("openid", openid);
            reqdata.setReqEntity("template", template);
            reqdata.setReqEntity("map", map);
            reqdata.setReqEntity("result", result);
            dubboServiceTools.getResult("3404", reqdata);
        }catch (Exception e){
            logger.info("存储微信消息状态失败"+e);
        }
        
    }

}
