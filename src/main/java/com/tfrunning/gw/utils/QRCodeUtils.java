package com.tfrunning.gw.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.tfrunning.gw.controller.WechatController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import javax.imageio.IIOException;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Base64;
import java.util.Hashtable;

public class QRCodeUtils {
    private static Logger logger = LoggerFactory.getLogger(QRCodeUtils.class);
    private static final String CHARSET = "utf-8";
    private static final String FORMAT_NAME = "JPG";
    // 二维码尺寸
    private static final int QRCODE_SIZE = 300;
    public static final String NATIVE_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/native";
    public static final String JSAPI_URL = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
    public static final String NOTIFY_URL = "http://wechat.microcredchina.com/SitMicrocred/wxpay/payNotifyForNative";
    public static final String NOTIFY_URL_s = "http://wechat.microcredchina.com/SitMicrocred/wxpay/payNotifyForNativeSC";
    public static final String certForEncrypt = "-----BEGIN CERTIFICATE-----\n" +
            "MIID9jCCAt6gAwIBAgIUIOpGySD0Fh1lC5PAQCFhD5y9ofowDQYJKoZIhvcNAQEL\n" +
            "BQAwXjELMAkGA1UEBhMCQ04xEzARBgNVBAoTClRlbnBheS5jb20xHTAbBgNVBAsT\n" +
            "FFRlbnBheS5jb20gQ0EgQ2VudGVyMRswGQYDVQQDExJUZW5wYXkuY29tIFJvb3Qg\n" +
            "Q0EwHhcNMjAxMTA1MDc1NTI1WhcNMjUxMTA0MDc1NTI1WjCBhzETMBEGA1UEAwwK\n" +
            "MTQ4NTI2MDAzMjEbMBkGA1UECgwS5b6u5L+h5ZWG5oi357O757ufMTMwMQYDVQQL\n" +
            "DCrlpKfov57lkIzmlrnova/pk7bnp5HmioDogqHku73mnInpmZDlhazlj7gxCzAJ\n" +
            "BgNVBAYMAkNOMREwDwYDVQQHDAhTaGVuWmhlbjCCASIwDQYJKoZIhvcNAQEBBQAD\n" +
            "ggEPADCCAQoCggEBALSUV8nmjUrclX9/L+EkXNW101WF6tWQR7YWNWPihLW2X1g7\n" +
            "GhGsXb2oQM5ux7ejMCAzNCF02fn7mBRb3ky5uf+ys/fUJzCx8BaRENjutoVzYXpU\n" +
            "XdbjiOKyyU7207kBdemxdtDQphS4yJBaPL1StiNdXkFdpmZWwXiA9qUr2/z6qVYz\n" +
            "t3KF++U5/1ogfNmW/kr/B5JpQjSD+PZSUbkpagYkKbHRPmthz2vE1OK7pUKBGhbr\n" +
            "g+nl4lz2EIO2tRkp2UWcZIa/1nSYZgsVoOkZjpEHDOr5Lwf/Q+94D7KEui4Xma6g\n" +
            "Slz9F1mJmk29z/Xhj/y/IQJwvbmbmJ7pnG95U0ECAwEAAaOBgTB/MAkGA1UdEwQC\n" +
            "MAAwCwYDVR0PBAQDAgTwMGUGA1UdHwReMFwwWqBYoFaGVGh0dHA6Ly9ldmNhLml0\n" +
            "cnVzLmNvbS5jbi9wdWJsaWMvaXRydXNjcmw/Q0E9MUJENDIyMEU1MERCQzA0QjA2\n" +
            "QUQzOTc1NDk4NDZDMDFDM0U4RUJEMjANBgkqhkiG9w0BAQsFAAOCAQEAnrUULOS4\n" +
            "N1zHC84xaEgC+NvLrRsyamw4X7avMrkxUdLoZ5M1oFQTuVpzOemVs6oa54juMRMz\n" +
            "tbgZbc4H5+2bvX6f6ciQN+l3Q5pngjlzqz+X9vYqyO3zi/ftSdqy7rh9Cft2k1YD\n" +
            "3z7Qf5PxhvlvJgXybIIvLXaIVa2ZYV5lV/DHYVH7Eh7PlXGYHds6HfUBECL9Xphg\n" +
            "GRuaegvNEhyUd7hy3h85IfetGxTf2weLgVpXCxOHs17i9Oo3XS3bqd8gT/Yicxm0\n" +
            "2oQQKyMExJCHcTqmZ+zptRMeQ3DhR8TbV78xFUPdri7EDX0ipZHmyy7aFqceM5/0\n" +
            "DSqoS4lFvubZvg==\n" +
            "-----END CERTIFICATE-----\n"; // 用于测试加密功能的证书
    public static final String privateKey ="-----BEGIN PRIVATE KEY-----\n" +
        "MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDMTVsig6ADyc5W\n" +
        "XnGsiWsnOD+Xa8nnitvWvprBhKjIr0r4pxe4J6viVrPkDSiwwCVheExcnCMT+CP2\n" +
        "iAJMkBkWfSzDtCGmzZuvTn7VW5V7ojVkD3TUruB2zN13e87367gO/EfmoCY6eUZW\n" +
        "1W1iQM43cj4U3ElBjTnRfEW72oScKKns2GCzMRq9zTA7e3UQCLH8Ulhr+n9bHGs1\n" +
        "kUQVKGLyN7LPSLSkkiG7rZqqFC18hM9Dg0t1Us5A8vuvloSAM4sZ23fINpLb2Xn6\n" +
        "JThxaNwVpvi1U18LRIQIpEmvR/EnN5m+SKlc8Cg/1tPsyBVcxiq4G76kBtXMjgHM\n" +
        "k7wiPeZNAgMBAAECggEAEG58UPdTLFrTzKCF9gGLa7kbbDtQHt5NNiVYXw4jYRiw\n" +
        "J/WQc9BFA8xDZaklR6n8l61MPvqT26xOgCqKPMwuKF/i9M6NElRhHCVtEA3wpOPz\n" +
        "9VtE40I5bXSmr1B24fvEbvgddFAXIwbUAHHj/U4HB/CZS6Y4AhywtMeQ+D3oAoMh\n" +
        "xQuTvN0K4fzAvvoGLBPr0NUlzNfXMaSi0fYqDlMTgxd5LmnVU0BJde0C6ITIrv52\n" +
        "N/+o9a8krFZgt1N8jx/55+mh9zxvUlSeeTXZYUcScOOCfB39hfeiG1UmfqAUzq5d\n" +
        "xzCfmeYIxJ2fHbVhnzebkNjJylGt5+ZBjV5srltloQKBgQD9PJzjLV+ERWkggAR6\n" +
        "SESczNVL+zuA2bzhQwdYiWrSV9kYWQrDalv7le8O5UNgsjbqWX38QXk9k9po8t+H\n" +
        "eeFBLEtErCGKpHnr4XQ8LK2M+Kkg9jP6drP7ryWo33mvuBk6ozpSwNnfQ7zHPXeb\n" +
        "2sXfKCIdTGM2FAzRgw2nWb0rtQKBgQDOiAzTzldCG1pcYpjfYhv+zsrGTyebZ2XU\n" +
        "hK302NwGUqLHcOYYnJRuy1ZHgWsvyCkFYi7WNC9Rd2Cxix4N8fITJiq/bY8nEXHf\n" +
        "5IOYNocpO36T0Y4lAeH6GL3Ss5vF10mr6aSqUKFyk1765J4R1d3GLHFd+UxJ0zV/\n" +
        "kVs9+qtfOQKBgQCKTh1dUb2WDxawYlqPsoKOpKxluAuyfrjSynnrl/mYcdQmoRLo\n" +
        "0WD1UdjjaMFYwVyOt1vAdr3RrLufj+4XHrCwmJFvKxeSkevosRR7fOc4U+KDFxQi\n" +
        "WcxIzEqkJZFfMAvPY7CJ27viXStwIZj+rQherZucJskUkvTBRHMjkDZp9QKBgD/x\n" +
        "yZ7ufP+/vktqT5CiLK7I0elyan3oFpQk/EMZvY3LmhCL+QHXwPFIzY7YGBq0K1c6\n" +
        "Xev+PkRH5M0zmHuS1HNvVr7sKe1brlk2xDOldlWi0P7tW8hDe/bWIDeTWoB3tpye\n" +
        "e1gsHMB/tmGR1QsKn16TNgp1fFRDK4YyuABJkzOpAoGBAJYkQY3J6BaA9wNHKnOg\n" +
        "q2N+xRrvWFaidotm39+81jOvawKYCkJN7MfKnaG5BSX71QMfk+hwh8XDka1lIODq\n" +
        "zQ09fi/tWE9Op0/ev0X/vihnRFqhcpYF9k/TxtcBKpA4vq1ixvjZIyexl0dDPJ47\n" +
        "Zlz35uI2A8I12Rv3lPsiAT+j\n" +
        "-----END PRIVATE KEY-----\n";
    public static final String privateKey_s ="-----BEGIN PRIVATE KEY-----\n" +
        "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCgQD7zEKy1rfjP\n" +
        "atHHZLk5ea4yVeWw/jiJaNPkEELruM4YhLllygXFD6Lf77kWt0mBP8vw2VvDbVGS\n" +
        "F7BmNwJ2eOg9MBEQIF6xOmWzZNxBhXDy7R/Xl1aMRn1yYJlurqw4RrjyMEXRZ9vH\n" +
        "r3q/aqjWHBNTc2Z6Sb+TQLRLiSz0HPmUnnmxuw85bU2Mtz9TLQJ7dUpAKD1/yw0O\n" +
        "9AHhrbbCcHgoeIN5DdYJUy7h4NmkERDWw6vJdxsBGbXs4KjWGVif5Vn4toOaF8Gd\n" +
        "I7RikF2Suf6sr8l3v0KGSwxrje0w/IKVVE8H6hyukeKxnZY1nlJE1WKsLtAJUkCw\n" +
        "6A5bwI8zAgMBAAECggEAOljqEqpBmCzt6Oel1gnQfyLU0DMCXL1TbISkkedx1k4F\n" +
        "jFvW/T7fdu7IiOzJ4YF+2/aUlO8vQCqvhpflfHIPDX/cX1r++3YtxC3VFcplf93M\n" +
        "UEcSTvclZhHMXzbY5rnmrt7hPB1jbml4Vm6ILOXy73JtfvcHmwL7I+xtOQuLOJ44\n" +
        "nqDOWvkU2sWudVdQHV3cFT1dvsBQ79uhlMDmH+1payOGxwWCt1jXfZtmoUHxAPbX\n" +
        "U9QPw2Nmra4EQ0TknyxVSdA71Vvs5KukPxc3igILLA67ur6aBi0r8W7NfDIzv2v6\n" +
        "JI/RpF1+hMUm3i7Gc8iH92WYnAIcAZhxCTAfvPrAoQKBgQDPu1+EALhGMiZDXhIf\n" +
        "tlD+mNHVHpewVs+L6JqIax/yEXA8hTz0BApZDG4fedrFMRLa33Ic5udBR7duIDXC\n" +
        "mfTolwuhApjKECfdGoQc9ArwVy7qgJolxOI1QjkoxS+ZJS0McJS/F6YVdKsPH6dT\n" +
        "tsG0yd0emVCEjELC9GBOlnuoKwKBgQDFfImj1iMUV9H67915glFYq6EhMPLOFhKv\n" +
        "CqTMr0/O8Rp5tLz9dNjCSck6cQILEBnHkGs0Cw9H+oTeimAaOUy/9ZDm+Ay/8q7Y\n" +
        "9Wwsq+RRKod0dD1pdPKzSpsuTvqIo/v9JLSueC6KY2GKj2QW/y8N9Qqs+tRAw2mJ\n" +
        "xxMUO9rpGQKBgE5dPgwbBWkdtAeH7kheS7GvzsCaGNkh9ouuBNXfq55y66c5ECRx\n" +
        "fcHYCZcALgJjn9xHnNHAXY7n0g/OQNmXoKc5i96Cj0byz0b+oMYXXiUKjp1SRFRp\n" +
        "MHis8ckqkekw+AyQT9zCvBmt+HXtF/9hmzNrT2oiphhKnUjUTZx38lAfAoGAaxd8\n" +
        "wEa4ISj6aly2ulSr7iysJQaZNJ6NpTRdZ83NtofSdptHPR6nCLzgjXuIwx0JDcpI\n" +
        "CCHTOJImiVN6h934u8PbvxRWPaTJokd8UMEp/8XTTJhYUR6lGgrkGfmkLILYfE7y\n" +
        "WAY+HreDxpK5kSryhlwSXPlsLm6JhbhZpUhMDskCgYEAzF6tzdDovDNEyL+0bEBx\n" +
        "5vjIHYu785dvK2VPkb0nhpxeUUyYCt23ARomLj5JKGGp7bJ7LDqgSdtyNVRdkcrl\n" +
        "uwi8pqs8Si7pvmkVZmXDgH7K+fomdrFtEWVTtNK1uGHrilFCuYOnCf1odfQHWijv\n" +
        "quTfVtyTN2P4qZb+m8qoDbg=\n" +
        "-----END PRIVATE KEY-----"; // 商户私钥
    //生产merchantId，APPID--开始
    public static final String merchantId = "1603820235"; // 商户号
    public static final String APPID = "wx5a8134a9d7af0674";
    public static final String merchantId_s = "1603816283"; // 商户号
    public static final String APPID_s = "wx5a8134a9d7af0674";
    //生产merchantId，APPID--结束
    //fnnc测试--开始
//    public static final String merchantId = "1603816283";
//    public static final String APPID = "wx22b0fd1f67eb2522";
//    public static final String merchantId_s = "1603816283";
//    public static final String APPID_s = "wx22b0fd1f67eb2522";
    //fnnc测试--结束
    //test
//    public static final String APPID = "wx22b0fd1f67eb2522";
    //1B0B4B8C7B6647D683860F60E79F283F0690D176,6973D117BC3770C930AA745BE3A37E902A75E960
    public static final String merchantSerialNumber = "1B0B4B8C7B6647D683860F60E79F283F0690D176"; // 商户证书序列号
    public static final String apiV3Key = "8856ae92c3134cebae36a5c960bbff88"; // apiV3密钥
    public static final String wechatPaySerial = "4774441ACE9020E7A896BEACADCBAC8F8DBA7C54"; // 平台证书序列号
    //1B0B4B8C7B6647D683860F60E79F283F0690D176,6973D117BC3770C930AA745BE3A37E902A75E960
    public static final String merchantSerialNumber_s = "2EBA42A4C534C5D07EF1A52CCCDE08267C948456"; // 商户证书序列号
    public static final String apiV3Key_s = "6656ae92c3134cebae36a5c960bbff66"; // apiV3密钥
    public static final String wechatPaySerial_s = "1F4DB50FDE0284E1D6660E3CD9641EB5378A66A3"; // 平台证书序列号





    /**
     * 生成二维码图片
     * @param url 扫描二维码跳转地址
     * @return
     * @throws Exception
     */
    public static BufferedImage createImage(String url) throws Exception {
        Hashtable hints = new Hashtable();
        hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.H);
        hints.put(EncodeHintType.CHARACTER_SET, CHARSET);
        hints.put(EncodeHintType.MARGIN, 1);
        BitMatrix bitMatrix = new MultiFormatWriter().encode(url, BarcodeFormat.QR_CODE, QRCODE_SIZE, QRCODE_SIZE,
                hints);
        int width = bitMatrix.getWidth();
        int height = bitMatrix.getHeight();
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                image.setRGB(x, y, bitMatrix.get(x, y) ? 0xFF000000 : 0xFFFFFFFF);
            }
        }
        return image;
    }

    /**
     * 输出二维码
     * @param url 扫描二维码跳转地址
     * @param response
     * @throws Exception
     */
    public static void encode(String url, HttpServletResponse response)
            throws Exception {
        BufferedImage image = QRCodeUtils.createImage(url);
        ImageIO.write(image, FORMAT_NAME, response.getOutputStream());
    }

    public static void main(String[] args) throws Exception {
        String url = "weixin://wxpay/bizpayurl?pr=VBFPiwCzz";
        BufferedImage image = QRCodeUtils.createImage(url);
//        String base64 = QRCodeUtils.getBase64FromImage(image);
//        System.out.print(base64);
        String destPath = "D:\\test\\er.jpg";
        ImageIO.write(image, FORMAT_NAME, new File(destPath));
    }

    /**
     * 生成二维码
     * @param url
     * @return
     * @throws Exception
     */
    public static String createQRCodeAsBase64(String url) throws Exception {
        BufferedImage image = QRCodeUtils.createImage(url);
        return QRCodeUtils.getBase64FromImage(image);
    }


    //BufferedImage 转base64
    public static String getBase64FromImage(BufferedImage img) throws IOException {
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try {
            ImageIO.write(img, "jpg", stream);
            BASE64Encoder encoder = new BASE64Encoder();
            return encoder.encode(stream.toByteArray());
        }catch (IOException e){
            logger.error("二维码转base64异常", e);
        }finally {
            stream.close();
        }
        return "";
    }

}
