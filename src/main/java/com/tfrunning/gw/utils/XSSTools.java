package com.tfrunning.gw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.HashSet;
import java.util.Set;

@Service
public class XSSTools {

    private static Logger logger = LoggerFactory.getLogger(XSSTools.class);

    private static String key = "and|exec|execute|insert|select|delete|update|count|drop|chr|mid|master|truncate|from|" +
            "char|declare|or|+|--|like" +
            "union|where|order|by|" +
            "%|#";

    private static Set<String> notAllowedKeyWords = new HashSet<String>(0);
    private static String replacedString="INVALID";
    static {
        String keyStr[] = key.split("\\|");
        for (String str : keyStr) {
            notAllowedKeyWords.add(str);
        }
    }

    public static String cleanXSS(String valueP) {
        if(!Tools.isNull(valueP)){
            for (String keyword : notAllowedKeyWords) {
                if (valueP.length() > keyword.length() + 4
                        && (valueP.contains(" "+keyword)||valueP.contains(keyword+" ")||valueP.contains(" "+keyword+" "))) {
                    valueP = StringUtils.replace(valueP, keyword, replacedString);
                }
            }
        }
        return valueP;
    }

}
