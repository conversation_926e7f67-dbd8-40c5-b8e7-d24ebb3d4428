package com.tfrunning.gw.utils;


import org.apache.commons.codec.binary.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@PropertySource(value ="classpath:application.properties" ,ignoreResourceNotFound = true)
@Service
public class BaiduTools {

    private Logger logger = LoggerFactory.getLogger(BaiduTools.class);
    private static String ACCESS_TOKEN = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={client_id}&client_secret={client_secret}";
    private static String VERIFY_TOKEN = "https://aip.baidubce.com/rpc/2.0/brain/solution/faceprint/verifyToken/generate?access_token={access_token}";

    //人脸识别结果
    private static String QUERY_RESULT_DETAIL = "https://aip.baidubce.com/rpc/2.0/brain/solution/faceprint/result/detail?access_token={access_token}";

    //人脸识别结果简易结果，返回照片的
    private static String QUERY_RESULT_SIMPLE = "https://aip.baidubce.com/rpc/2.0/brain/solution/faceprint/result/simple?access_token={access_token}";


    //身份证与名字比对
    private static String IDMATCH_URL = "https://aip.baidubce.com/rest/2.0/face/v3/person/idmatch?access_token={access_token}";

    //身份证与名字比对
    private static String OCRIDCARD_URL = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token={access_token}";


    // 指定用户信息上报接口

    private static String IDCARD_SUBMIT = "https://brain.baidu.com/solution/faceprint/idcard/submit";

    //人脸识视频接口
    private static String QUERY_MEDIA_RESULT = "https://aip.baidubce.com/rpc/2.0/brain/solution/faceprint/result/media/query?access_token={access_token}";


    public String getAPP_ID() {
        return APP_ID;
    }

    @Value("${baidu.app_id}")
    private String APP_ID;

    @Value("${baidu.api_key}")
    private String API_KEY;

    @Value("${baidu.secret_key}")
    private String SECRET_KEY;

    @Value("${baidu.plan_id}")

    private String PLAN_ID;

    @Value("${baidu.plan_id_simple}")
    private String PLAN_ID_SIMPLE;

    @Autowired
    Environment environment;


    @Autowired
    RedisTools redisTools;

    @Autowired
    private IdWorker idWorker;


    /**
     * 获取ACCESS_TOKEN
     * */
    public String getAccessToken(){
        Map sigparam = new HashMap();

        sigparam.put("client_id", API_KEY);
        sigparam.put("client_secret", SECRET_KEY);

        String token = null;

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();

            Map sigRes = tfRestTemplate.getForObject(ACCESS_TOKEN, Map.class, sigparam);
            logger.info(String.format("获取百度access_token返回结果:%s",sigRes.toString()));
            String errcode = (String) sigRes.get("error");
            if(errcode!=null){
                logger.info(String.format("获取百度access_token返回错误信息：%s",(String)sigRes.get("error_description")));
                return null;
            }

            String access_token = (String) sigRes.get("access_token");
            if(!Tools.isNull(access_token)){
                redisTools.set("baidu_access_token",access_token);
                token = access_token;
            }else{
                logger.info("返回百度access_token为空！！！！！！！");
            }
        }catch (Exception e){
            logger.info("获取百度access_token失败："+e.getMessage(),e);
        }
        return token;
    }


    /**
     * 获取VERIFY_TOKEN
     * */
    public String getVerifyToken(String access_token){
        Map sigparam = new HashMap();

        sigparam.put("access_token", access_token);

        String token = null;

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();


           // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HashMap<String, Object> map = new HashMap<>();
            map.put("plan_id", PLAN_ID);

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);

            Map sigRes = tfRestTemplate.postForObject(VERIFY_TOKEN, request,Map.class, sigparam);

            logger.info(String.format("获取百度verify_token返回结果:%s",sigRes.toString()));
            Boolean success = (Boolean) sigRes.get("success");
            if(!success){
                logger.info("获取百度verify_token返回错误");
                return null;
            }

            Map result = (HashMap) sigRes.get("result");
            if(result==null){
                logger.info("获取百度verify_token返回错误");
                return null;
            }else{
                token=(String)result.get("verify_token");
            }


        }catch (Exception e){
            logger.info("获取百度verify_token失败："+e.getMessage(),e);
        }
        return token;
    }

    /***
     *
     * @param access_token
     * @param type  normal 标准级活体视频   simple 图片
     * @return
     */
    public String getVerifyToken(String access_token,String type){
        Map sigparam = new HashMap();

        sigparam.put("access_token", access_token);

        String token = null;

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();


            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);

            HashMap<String, Object> map = new HashMap<>();
            if("normal".equals(type)) {
                map.put("plan_id", PLAN_ID);
            }else if("simple".equals(type)) {
                map.put("plan_id", PLAN_ID_SIMPLE);
            }

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);

            logger.info(String.format("获取百度verify_token请求数据:%s",request.toString()));
            Map sigRes = tfRestTemplate.postForObject(VERIFY_TOKEN, request,Map.class, sigparam);

            logger.info(String.format("获取百度verify_token返回结果:%s",sigRes.toString()));
            Boolean success = (Boolean) sigRes.get("success");
            if(!success){
                logger.info("获取百度verify_token返回错误");
                return null;
            }

            Map result = (HashMap) sigRes.get("result");
            if(result==null){
                logger.info("获取百度verify_token返回错误");
                return null;
            }else{
                token=(String)result.get("verify_token");
            }


        }catch (Exception e){
            logger.info("获取百度verify_token失败："+e.getMessage(),e);
        }
        return token;
    }


    public RespTfData queryFaceResultDetail(String access_token, String verify_token){
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("获取百度人脸识别结果成功");

        Map sigparam = new HashMap();

        sigparam.put("access_token", access_token);

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();


            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> map = new HashMap<>();
            map.put("verify_token", verify_token);

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);

            Map sigRes = tfRestTemplate.postForObject(QUERY_RESULT_DETAIL, request,Map.class, sigparam);

            logger.info(String.format("获取百度人脸识别结果返回:%s",sigRes.toString()));

            Map result = (HashMap) sigRes.get("result");
            if(result==null){
                logger.info("百度人脸识别发生错误，没有返回具体信息");
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("百度人脸识别发生错误，没有返回具体信息");
                return respTfData;
            }else{
                Map verify_result = (HashMap) result.get("verify_result");
                Double score= new BigDecimal(String.valueOf(verify_result.get("score"))).doubleValue();//人脸实名认证分数
                Double liveness_score= new BigDecimal(String.valueOf(verify_result.get("liveness_score"))).doubleValue();//活体检测分数分数
                respTfData.setRespEntity("score",score);
                respTfData.setRespEntity("liveness_score",liveness_score);
            }


        }catch (Exception e){
            logger.info("百度人脸识别异常："+e.getMessage(),e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("百度人脸识别异常："+e.getMessage());
        }
        return respTfData;

    }


    public RespTfData queryFaceResultSimple(String access_token, String verify_token){
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("获取百度人脸识别结果成功");

        Map sigparam = new HashMap();
        sigparam.put("access_token", access_token);

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();


            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> map = new HashMap<>();
            map.put("verify_token", verify_token);

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);

            Map sigRes = tfRestTemplate.postForObject(QUERY_RESULT_SIMPLE, request,Map.class, sigparam);

            logger.info(String.format("获取百度人脸识别结果(simple)返回:%s",sigRes.toString()));

            Boolean success = (Boolean) sigRes.get("success");
            if(!success){
                String message=(String) sigRes.get("message");
                logger.info(String.format("百度人脸识别失败:%s",message));
                respTfData.setRespCode("9999");
                respTfData.setRespDesc(String.format("百度人脸识别失败:%s",message));
                return respTfData;
            }

            Map result = (HashMap) sigRes.get("result");
            if(result==null){
                logger.info("百度人脸识别发生错误，没有返回具体信息");
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("百度人脸识别发生错误，没有返回具体信息");
                return respTfData;
            }else{
                String image = (String) result.get("image");
                respTfData.setRespEntity("image",image);
            }


        }catch (Exception e){
            logger.info("百度人脸识别失败："+e.getMessage(),e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("百度人脸识别失败："+e.getMessage());
        }
        return respTfData;

    }


    /**
     * 身份证与姓名比对
     * @param id_card_number 身份证号码
     * @param name  姓名（注：需要是UTF-8编码的中文）
     * @return
     */
    public RespTfData queryIDMatch(String id_card_number, String name){
        String access_token = redisTools.get("baidu_access_token");
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("获取身份证与姓名比对成功");

        Map sigparam = new HashMap();
        sigparam.put("access_token", access_token);


        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();

            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> map = new HashMap<>();
            map.put("id_card_number", id_card_number);
            map.put("name", name);

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);

            Map sigRes = tfRestTemplate.postForObject(IDMATCH_URL, request,Map.class, sigparam);
            logger.info(String.format("获取身份证与姓名比对结果返回:%s",sigRes.toString()));

            String error_code = String.valueOf(sigRes.get("error_code"));

            if("0".equals(error_code)){
                respTfData.setRespCode("0000");
                respTfData.setRespDesc("获取身份证与姓名比对成功");

            }else{
                String error_msg = environment.getProperty(error_code);
                respTfData.setRespCode("9999");
                respTfData.setRespDesc(error_msg);
            }
            return respTfData;
        }catch (Exception e){
            logger.info("百度人脸识别失败："+e.getMessage(),e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("百度人脸识别失败："+e.getMessage());
            return respTfData;
        }

    }


    /**
     * 身份证OCR
     * @param imgBase64 图片base64数据
     * @param type  类型 front 含照片那面  back 国徽那面
     * @return
     */
    public RespTfData OCRIDcard(String imgBase64, String type){
        String access_token = redisTools.get("baidu_access_token");
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("身份证OCR成功");

        Map sigparam = new HashMap();
        sigparam.put("access_token", access_token);


        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();
            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

            //HashMap来作为body传递 解析转换不了，换成 MultiValueMap
            MultiValueMap<String, Object> map = new LinkedMultiValueMap<>();
            map.add("image",imgBase64);
            map.add("id_card_side",type);

            //用HttpEntity封装整个请求报文
            HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(map, headers);

            Map sigRes = tfRestTemplate.postForObject(OCRIDCARD_URL, request,Map.class, sigparam);
            logger.info(String.format("获取身份证OCR结果返回:%s",sigRes.toString()));

            String error_code =  String.valueOf(sigRes.get("error_code"));

            if(error_code==null||error_code.equals("null")){//代表成功
                respTfData.setRespCode("0000");
                respTfData.setRespDesc("获取身份证OCR成功");
                respTfData.setParmMap(sigRes);
            }else{
                String error_msg = environment.getProperty(error_code);
                respTfData.setRespCode("9999");
                respTfData.setRespDesc(error_msg);

            }
            return respTfData;
        }catch (Exception e){
            logger.info("获取身份证OCR失败："+e.getMessage(),e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取身份证OCR失败："+e.getMessage());
            return respTfData;
        }

    }

    /**
     *  获取身份过期日期
     * @param imgBase64 图片数据
     * @return
     */
    public RespTfData getIDCardExpiringDate(String imgBase64){

        try{
            RespTfData respTfData= OCRIDcard(imgBase64,"back");

            if(!respTfData.getRespCode().equals("0000")){

                return respTfData;
            }else{

                HashMap words_result=(HashMap)respTfData.getParmMap().get("words_result");
                HashMap ExpiringDateMap=(HashMap)words_result.get("失效日期");
                String certteddate=(String)ExpiringDateMap.get("words");
                respTfData.setRespEntity("certteddate",certteddate);

            }

            return respTfData;
        }catch (Exception e){
            logger.info("获取身份过期日期失败："+e.getMessage(),e);
            RespTfData respTfData=new RespTfData();
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取身份过期日期失败："+e.getMessage());
            return respTfData;
        }
    }

    /**
     *
     * */
    public RespTfData idcardSubmit(String verify_token,String id_name,String id_no){
        RespTfData respTfData = new RespTfData();
        respTfData.setRespCode("0000");
        respTfData.setRespDesc("指定用户信息上报接口成功");

        Map sigparam = new HashMap();

        try{
            RestTemplate tfRestTemplate = Tools.getRestTemplate();


            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> map = new HashMap<>();
            map.put("verify_token", verify_token);
            map.put("id_name", id_name);
            map.put("id_no", id_no);
            map.put("certificate_type", 0);


            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);
            Map sigRes = tfRestTemplate.postForObject(IDCARD_SUBMIT, request,Map.class, sigparam);
            logger.info(String.format("指定用户信息上报接口:%s",sigRes.toString()));

            Boolean success = (Boolean) sigRes.get("success");
            if(!success){
                logger.info("指定用户信息上报接口失败");
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("指定用户信息上报接口失败");
                return respTfData;
            }else{
                return respTfData;
            }

        }catch (Exception e){
            logger.info("百度人脸识别失败："+e.getMessage(),e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("指定用户信息上报接口失败："+e.getMessage());
        }
        return respTfData;

    }

    /**
     * 查询百度人脸识别视频
     * 返回值demo：
     * {
     * 	success=true,
     * 	result= {
     * 		processVideo=[https://bj.bcebos.com/v1/face-h5/h5-video-temp/verify_video/202209/15276/GgZEy?authorization=b71679f2231093fc71],
     * 		images=[
     * 			https://bj.bcebos.com/v1/face-h5/h5-video-temp/verif00.jpg?authorization=22a2F86400%2F%2F179e0d1968da3e905de2beae979d202d8afe030699b39f7ae2e2cfecd1519eca,
     * 			https://bj.bcebos.com/v1/face-h5/h5-video-temp/verify_im2tU/01.jpg?authorization=Z%2F86400%2F%2F839cbcb168dae954fb90743918a972cbe0205220444e8a5a7a5767dfcde2c3ca,
     * 			https://bj.bcebos.com/v1/face-h5/h5-video-temp/verify_iYO/02.jpg?authorization=%2F86400%2F%2Fa1458c7f40e0adb5ce68a6f70b810f88407eeb81b2321db3b7132253e850a3ca,
     * 			https://bj.bcebos.com/v1/face-h5/h5-video-temp/vg/03.jpg?authorization=7a994119102513faaee
     * 		],
     * 		video=null,
     * 		extInfo=null
     *        },
     * 	log_id=1571035144591075867
     * }
     * @param access_token
     * @param verify_token
     * @return
     */
    public RespTfData queryMediaResult(String access_token, String verify_token){
        RespTfData respTfData = new RespTfData();

        try{
            Map<String, String> uriParamsMap = new HashMap<>();
            uriParamsMap.put("access_token", access_token);
            // 设置请求头，请求类型为json
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HashMap<String, Object> map = new HashMap<>();
            map.put("verify_token", verify_token);

            //用HttpEntity封装整个请求报文
            HttpEntity<HashMap<String, Object>> request = new HttpEntity<>(map, headers);
            RestTemplate tfRestTemplate = Tools.getRestTemplate();
            Map sigRes = tfRestTemplate.postForObject(QUERY_MEDIA_RESULT, request, Map.class, uriParamsMap);

            if(null == sigRes){
                logger.info("获取百度人脸识别视频失败,请求返回空");
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("获取百度人脸识别视频失败,请求返回空");
            }else{
                logger.info(String.format("获取百度人脸识别视频结果(media)返回:%s", sigRes.toString()));
                String success = sigRes.get("success") + "";

                if(!StringUtils.equals("true", success)){
                    String message = sigRes.get("message") + "";
                    logger.info(String.format("获取百度人脸识别视频失败:%s", sigRes.get("message") + ""));
                    respTfData.setRespCode("9999");
                    respTfData.setRespDesc(String.format("获取百度人脸识别视频失败:%s", message));
                }else{

                    if(null != sigRes.get("result")){
                        Map result = (HashMap) sigRes.get("result");

                        if(result.isEmpty()){
                            logger.info("获取百度人脸识视频别发生错误,没有返回具体信息");
                            respTfData.setRespCode("9999");
                            respTfData.setRespDesc("获取百度人脸识视频别发生错误,没有返回具体信息");
                        }else{
                            String[] processVideoArr;

                            if(null == result.get("processVideo")){
                                processVideoArr = new String[0];
                            }else{
                                String processVideo = result.get("processVideo").toString();

                                if("".equals(processVideo.trim())
                                        || "null".equalsIgnoreCase(processVideo)){
                                    processVideoArr = new String[0];
                                }else{
                                    processVideoArr = processVideo.substring(1, processVideo.length() - 1).split(",");
                                }
                            }
                            String[] videoArr;

                            if(null == result.get("video")){
                                videoArr = new String[0];
                            }else{
                                String video = result.get("video").toString();

                                if("".equals(video.trim())
                                        || "null".equalsIgnoreCase(video)){
                                    videoArr = new String[0];
                                }else{
                                    videoArr = video.substring(1, video.length() - 1).split(",");
                                }
                            }
                            String[] imagesArr;

                            if(null == result.get("images")){
                                imagesArr = new String[0];
                            }else{
                                String images = result.get("images").toString();

                                if("".equals(images.trim())
                                        || "null".equalsIgnoreCase(images)){
                                    imagesArr = new String[0];
                                }else{
                                    imagesArr = images.substring(1, images.length() - 1).split(",");
                                }
                            }

                            Map<String, String[]> resultMap = new HashMap<>();
                            resultMap.put("processVideo", processVideoArr);
                            resultMap.put("video", videoArr);
                            resultMap.put("images", imagesArr);
                            respTfData.setRespEntity("data", resultMap);
                            respTfData.setRespCode("0000");
                            respTfData.setRespDesc("获取百度人脸识别视频结果成功");
                        }
                    }else{
                        logger.info("获取百度人脸识视频别发生错误,没有返回result对象");
                        respTfData.setRespCode("9999");
                        respTfData.setRespDesc("获取百度人脸识视频别发生错误,没有返回result对象");
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.info("获取百度人脸识别视频服务器异常：" + e.getMessage(), e);
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("获取百度人脸识别视频服务器异常：" + e.getMessage());
        }
        return respTfData;
    }
}