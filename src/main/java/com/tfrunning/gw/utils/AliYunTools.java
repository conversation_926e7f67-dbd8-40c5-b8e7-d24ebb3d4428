package com.tfrunning.gw.utils;

import com.tfrunning.gw.service.AsyncService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 阿里人脸识别处理工具类
 * <AUTHOR>
 * @Date: 2023/9/14/14:12
 */
@Service
public class AliYunTools {
    private Logger logger = LoggerFactory.getLogger(AliYunTools.class);
    @Autowired
    RedisTools redisTools;
    @Autowired
    private DubboServiceTools dubboServiceTools;
    @Autowired
    AsyncService asyncService;

    /**
     * 人脸识别认证信息处理
     * @param hMap
     * @return
     */
    public RespTfData aliFaceAuth(HashMap hMap){
        ReqTfData reqdata = new ReqTfData();
        RespTfData respdata=new RespTfData();

        //前端参数
        String cifid=(String) hMap.get("cifid");//客户编号
        String opname = (String) hMap.get("opname");//未解密的openid
        String sername = (String) hMap.get("sername");//token
        String openid = DESEncrypt.decrypt(opname);//解密后的openid

        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);
        reqdata.setReqEntity("wechatid", sername);
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);
        Tools.dealWithData(reqdata,hMap);

        RespTfData userInforespdata = dubboServiceTools.getResult("1050", reqdata);
        if("0000".equals(userInforespdata.getRespCode())) {
            String cliname = (String) userInforespdata.getRespEntity("cliname");
            String certno = (String) userInforespdata.getRespEntity("certno");
            reqdata.setReqEntity("certNo", certno);
            reqdata.setReqEntity("certName", cliname);
            RespTfData faceAuthResp = dubboServiceTools.getResult("3215", reqdata);
            if("0000".equals(faceAuthResp.getRespCode())){
                Map resultMap = (Map)faceAuthResp.getRespEntity("result");
                respdata.setRespEntity("certifyId", resultMap.get("certifyId"));
                respdata.setRespEntity("certifyUrl", resultMap.get("certifyUrl"));
                respdata.setRespCode("0000");
                respdata.setRespDesc("获取阿里人脸识别认证信息成功");
                return respdata;
            }else{
                return faceAuthResp;
            }
        }else{
            return userInforespdata;
        }
    }

    /**
     * 人脸识别认证结果查询处理
     * @param hMap
     * @return
     */
    public RespTfData aliFaceResult(HashMap hMap){
        ReqTfData reqdata = new ReqTfData();
        RespTfData respdata=new RespTfData();

        //前端参数
        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");
        String openid = DESEncrypt.decrypt(opname);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);//未解密的openid
        reqdata.setReqEntity("wechatid", sername);//token
        Tools.dealWithData(reqdata,hMap);
        try {
            RespTfData faceResultResp = dubboServiceTools.getResult("3220", reqdata);
            if(!"0000".equals(faceResultResp.getRespCode())) {
                return  faceResultResp;
            }
            Map resultMap = (Map)faceResultResp.getRespEntity("result");
            asyncService.sendFacePicture(openid,resultMap, (List<String>) hMap.get("serids"));

            respdata.setRespEntity("isSuccess", resultMap.get("isSuccess"));
            respdata.setRespEntity("subCodeReason", resultMap.get("subCodeReason"));
            respdata.setRespCode("0000");
            respdata.setRespDesc("获取阿里人脸识别认证结果查询成功");
        }catch (Exception e){
            respdata.setRespCode("7777");
            respdata.setRespDesc("获取阿里人脸识别认证结果查询异常");
        }
        return respdata;
    }
}
