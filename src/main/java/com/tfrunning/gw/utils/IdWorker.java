package com.tfrunning.gw.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * IdWorker snowflakeJAVA版本
 * 使用Twitter的分布式自增ID算法snowflake for Java edit by Dav
 * <AUTHOR>
 * @version V1.0 2016-9-9
 * @version V1.0.1 2018-09-22 Dav 添加备注来源
 */

public class IdWorker {
    private final static Logger logger = LoggerFactory.getLogger(IdWorker.class);
    private static int calltimes;
    /** 工作机器ID(0~31) */
    private long workerId;
    /** 数据中心ID(0~31) */
    private long datacenterId;
    /** 毫秒内序列(0~4095) */
    private long sequence = 0L;
    /** 开始时间截 (2015-01-01) */
    private long twepoch = 1288834974657L;
    /** 机器id所占的位数 */
    private long workerIdBits = 5L;
    /** 数据标识id所占的位数 */
    private long datacenterIdBits = 5L;
    /** 支持的最大机器id，结果是31 (这个移位算法可以很快的计算出几位二进制数所能表示的最大十进制数) */
    private long maxWorkerId = -1L ^ (-1L << workerIdBits);
    /** 支持的最大数据标识id，结果是31 */
    private long maxDatacenterId = -1L ^ (-1L << datacenterIdBits);
    /** 序列在id中占的位数 */
    private long sequenceBits = 12L;
    /** 机器ID向左移12位 */
    private long workerIdShift = sequenceBits;
    /** 数据标识id向左移17位(12+5) */
    private long datacenterIdShift = sequenceBits + workerIdBits;
    /** 时间截向左移22位(5+5+12) */
    private long timestampLeftShift = sequenceBits + workerIdBits + datacenterIdBits;
    /** 生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095) */
    private long sequenceMask = -1L ^ (-1L << sequenceBits);
    /** 上次生成ID的时间截 */
    private long lastTimestamp = -1L;

    /**
     * 构造函数
     *
     * @param workerId
     *            工作ID (0~31)
     * @param datacenterId
     *            数据中心ID (0~31)
     */
    public IdWorker(long workerId, long datacenterId) {
        // sanity check for workerId
        logger.info("workerId:" + workerId);
        logger.info("datacenterId:" + datacenterId);
        logger.info("idworker 生成了" + ++calltimes + "次对象");
        if (workerId > maxWorkerId || workerId < 0) {
            throw new IllegalArgumentException(
                    String.format("worker Id can't be greater than %d or less than 0", maxWorkerId));
        }
        if (datacenterId > maxDatacenterId || datacenterId < 0) {
            throw new IllegalArgumentException(
                    String.format("datacenter Id can't be greater than %d or less than 0", maxDatacenterId));
        }
        this.workerId = workerId;
        this.datacenterId = datacenterId;
        logger.info(String.format(
                "worker starting. timestamp left shift %d, datacenter id bits %d, worker id bits %d, sequence bits %d, workerid %d",
                timestampLeftShift, datacenterIdBits, workerIdBits, sequenceBits, workerId));
    }

    /**
     * 获得下一个ID (该方法是线程安全的)
     *
     * @return SnowflakeId
     */
    public synchronized long nextId() {
        long timestamp = timeGen();
        // System.out.println();
        if (timestamp < lastTimestamp) {// myl如果获取系统当前时间点小于上次时间点直接抛出异常，因为正常走程序最小也应该等于上次时间点
            System.out
                    .println(String.format("clock is moving backwards.  Rejecting requests until %d.", lastTimestamp));
            throw new RuntimeException(String.format(
                    "Clock moved backwards.  Refusing to generate id for %d milliseconds", lastTimestamp - timestamp));
        }

        if (lastTimestamp == timestamp) {
            // myl如果当前时间等于上次时间，那么需要获取一个增加序列，
            // 如果增加序列为0，那么重新获取当前时间直到当前时间大于上次时间
            sequence = (sequence + 1) & sequenceMask;
            if (sequence == 0) {
                timestamp = tilNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        return ((timestamp - twepoch) << timestampLeftShift) | (datacenterId << datacenterIdShift)
                | (workerId << workerIdShift) | sequence;
    }

    /**
     * 阻塞到下一个毫秒，直到获得新的时间戳
     *
     * @param lastTimestamp
     *            上次生成ID的时间截
     * @return 当前时间戳
     */
    protected long tilNextMillis(long lastTimestamp) {
        long timestamp = timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = timeGen();
        }
        return timestamp;
    }

    /**
     * 返回以毫秒为单位的当前时间
     *
     * @return 当前时间(毫秒)
     */
    protected long timeGen() {
        return System.currentTimeMillis();
    }

    public static void main(String[] args) {
        IdWorker iw = new IdWorker(2, 2);
        System.out.println(iw.nextId());
    }
}