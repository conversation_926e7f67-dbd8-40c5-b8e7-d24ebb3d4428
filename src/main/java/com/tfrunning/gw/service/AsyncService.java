package com.tfrunning.gw.service;

import com.tfrunning.gw.utils.*;
import org.apache.commons.codec.binary.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AsyncService {

    private static Logger logger = LoggerFactory.getLogger(AsyncService.class);

    @Autowired
    RedisTools redisTools;


    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    BaiduTools baiduTools;

    @Autowired
    private IdWorker idWorker;

    @Value("${custom.uploadfilepath}")
    private String downloadFolder;

    //获取身份证识别图片
    @Async
    public void getOCRPic(String orderNo) {
        logger.info("==========获取身份证图片异步操作开始==========");


    }

    //获取人脸识别图片
    @Async
    public void getFacePic(String orderNo) {
        logger.info("==========获取人脸识别图片异步操作开始==========");

    }

    @Async
    public void getLicense(String orderNo,String fileurl) {
        redisTools.set(orderNo+"-license",Tools.getImgStr(fileurl),Tools.EXPIRE_SECOND);

        File file = new File(fileurl);
        if (file.exists() && file.isFile()) {
            file.delete();
        }
    }



    //上传人脸识别中的图片
    @Async
    public void sendFacePic(String custId, String verify_token,List<String> serids) {
        logger.info("==========上传人脸识别中的图片异步操作开始==========");

        for(String serid :serids)
        {
            logger.info("==========serid=========="+serid);
        }
        String access_token = redisTools.get("baidu_access_token");
        RespTfData respTfData = baiduTools.queryFaceResultSimple(access_token,verify_token);
        if(respTfData.getRespCode().equals("0000")){
            String newFilePath=downloadFolder+idWorker.nextId()+".jpg";
            logger.info("下载人脸识别中的图片到本地："+newFilePath);
            String imageUrl=(String) respTfData.getRespEntity("image");
            boolean downLoadResult=Tools.downLoadFile(imageUrl,newFilePath);
            if(downLoadResult){
                ReqTfData reqdata = new ReqTfData();
                reqdata.setReqEntity("custId", custId);
                String cifid = redisTools.hget(custId,"cifid");
                reqdata.setReqEntity("cifid", cifid);
                reqdata.setReqEntity("bankid", Tools.BANKID);
                reqdata.setReqEntity("serids",serids);

                List<String> photos = new ArrayList<String>();
                photos.add(Tools.getImgStr(newFilePath));
                reqdata.setReqEntity("photos",photos);
                dubboServiceTools.getResult("1056", reqdata);
            }
        }else{
            logger.error("上传人脸识别中的图片："+respTfData.getRespDesc());
        }

        logger.info("==========上传人脸识别中的图片异步操作结束==========");
    }

    //上传人脸识别中的图片
    @Async
    public void sendFacePicture(String custId,Map imgMap,List<String> serids) {
        logger.info("==========上传人脸识别中的图片异步操作开始==========");
        for(String serid :serids)
        {
            logger.info("==========serid=========="+serid);
        }
        String imageUrl = (String) imgMap.get("pictureUrl");
        String newFilePath=downloadFolder+idWorker.nextId()+".jpg";
        logger.info("下载人脸识别中的图片到本地："+newFilePath);
        boolean downLoadResult=Tools.downLoadFile(imageUrl,newFilePath);
        if(downLoadResult){
            ReqTfData reqdata = new ReqTfData();
            reqdata.setReqEntity("custId", custId);
            String cifid = redisTools.hget(custId,"cifid");
            reqdata.setReqEntity("cifid", cifid);
            reqdata.setReqEntity("bankid", Tools.BANKID);
            reqdata.setReqEntity("serids",serids);

            List<String> photos = new ArrayList<String>();
            photos.add(Tools.getImgStr(newFilePath));
            reqdata.setReqEntity("photos",photos);
            dubboServiceTools.getResult("1056", reqdata);
        }

        logger.info("==========上传人脸识别中的图片异步操作结束==========");
    }

    /**
     * 上传人脸识别中的视频和图片
     * @param custId
     * @param verify_token
     * @param serids
     */
    @Async
    public void sendFaceVideo(String custId, String verify_token, List<String> serids) {
        Long startTime = System.currentTimeMillis();
        logger.info("上传人脸识别中的视频和图片异步操作开始,serids = " + serids);
        String access_token = redisTools.get("baidu_access_token");
        RespTfData respTfData = baiduTools.queryMediaResult(access_token, verify_token);

        if(StringUtils.equals("0000", respTfData.getRespCode())){

            if(null != respTfData.getRespEntity("data")){
                ReqTfData reqTfData = new ReqTfData();
                reqTfData.setReqEntity("custId", custId);
                reqTfData.setReqEntity("cifid", redisTools.hget(custId,"cifid"));
                reqTfData.setReqEntity("bankid", Tools.BANKID);
                reqTfData.setReqEntity("serids",serids);

                Map<String, String[]> dataMap = (Map<String, String[]>) respTfData.getRespEntity("data");
                String[] processVideoArr = dataMap.get("processVideo");

                if(null != processVideoArr
                        && 0 < processVideoArr.length){
                    List<Map<String, String>> processVideoList = new ArrayList<>();

                    for (int i = 0; i < processVideoArr.length; i++) {
                        String processVideo = processVideoArr[i];
                        //String suffix = processVideo.substring(processVideo.lastIndexOf("."));
                        String suffix = ".mp4";
                        String localProcessVideoPath = downloadFolder + idWorker.nextId() + suffix;
                        logger.info("下载人脸识别视频中的processVideo视频-" + (i + 1) + "到本地：" + localProcessVideoPath);
                        boolean downLoadResult = Tools.downLoadFile(processVideo, localProcessVideoPath);

                        if(downLoadResult){
                            Map<String, String> processVideoMap = new HashMap<>();
                            processVideoMap.put("path", Tools.getVideoStr(localProcessVideoPath));
                            processVideoMap.put("suffix", suffix);
                            processVideoMap.put("type", "video");
                            processVideoList.add(processVideoMap);
                        }
                    }

                    if(!processVideoList.isEmpty()){
                        reqTfData.setReqEntity("processVideoList", processVideoList);
                    }
                }
                String[] videoArr = dataMap.get("video");

                if(null != videoArr
                        && 0 < videoArr.length){
                    List<Map<String, String>> videoList = new ArrayList<>();

                    for (int i = 0; i < videoArr.length; i++) {
                        String video = videoArr[i];
                        //String suffix = video.substring(video.lastIndexOf("."));
                        String suffix = ".mp4";
                        String localVideoPath = downloadFolder + idWorker.nextId() + suffix;
                        logger.info("下载人脸识别视频中的video视频-" + (i + 1) + "到本地：" + localVideoPath);
                        boolean downLoadResult = Tools.downLoadFile(video, localVideoPath);

                        if(downLoadResult){
                            Map<String, String> videoMap = new HashMap<>();
                            videoMap.put("path", Tools.getVideoStr(localVideoPath));
                            videoMap.put("suffix", suffix);
                            videoMap.put("type", "video");
                            videoList.add(videoMap);
                        }
                    }

                    if(!videoList.isEmpty()){
                        reqTfData.setReqEntity("videoList", videoList);
                    }
                }
                String[] images = dataMap.get("images");

                if(null != images
                        && 0 < images.length){
                    List<Map<String, String>> imageList = new ArrayList<>();

                    for (int i = 0; i < images.length; i++) {
                        String image = images[i];
                        //String suffix = image.substring(image.lastIndexOf("."));
                        String suffix = ".jpg";
                        String localImagePath = downloadFolder + idWorker.nextId() + suffix;
                        logger.info("---下载人脸识别视频中的image图片-" + (i + 1) + "到本地：" + localImagePath);
                        boolean downLoadResult = Tools.downLoadFile(image, localImagePath);

                        if(downLoadResult){
                            Map<String, String> imageMap = new HashMap<>();
                            imageMap.put("path", Tools.getImgStr(localImagePath));
                            imageMap.put("suffix", suffix);
                            imageMap.put("type", "image");
                            imageList.add(imageMap);
                        }
                    }

                    if(!imageList.isEmpty()){
                        reqTfData.setReqEntity("imageList", imageList);
                    }
                }
                dubboServiceTools.getResult("1590", reqTfData);
            }else{
                logger.error("---上传人脸识别视频错误,返回0000,但是无实际数据返回");
            }
        }else{
            logger.error("---上传人脸识别视频：" + respTfData.getRespDesc());
        }
        logger.info("上传人脸识别视频异步操作结束,共耗时:" + (System.currentTimeMillis() - startTime) + "毫秒");
    }
}


