package com.tfrunning.gw.service;

import com.tfrunning.gw.SpringBeanUtil;
import com.tfrunning.gw.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 定时任务
 */
@Slf4j
@Service
@Configuration
@EnableScheduling
@EnableAsync
public class ScheduledService {

    private static Logger logger = LoggerFactory.getLogger(ScheduledService.class);

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    BaiduTools baiduTools;

    @Autowired
    private DubboServiceTools dubboServiceTools;


    @Autowired
    RedisTools redisTools;
    /***
     * 用于更新微信公众号access_token
     * 6000秒刷新一次
     */

    @Value("${weichat.template.repay}")
    private String TEMPLATE_REPAY; //消息模板  还款提醒

    @Value("${weichat.template.lnlo}")
    private String TEMPLATE_LNLO;  //消息模板 逾期提醒


    @Value("${weichat.template.couponlo}")
    private String TEMPLATE_COUPONLO; //消息模板  优惠券到期提醒

    @Value("${weichat.couponurl}")
    private String COUPONURL; //消息模板  优惠券链接地址

    @Value("${weichat.app_id}")
    private String APP_ID;


    @Async
    @Scheduled(fixedRate = 6000000)
    public void refreshAccessToken() {

        logger.info(String.format("==================刷新access_token开始================%s", Tools.getNowDateTime()));
        weiChatTools.getToken();
        logger.info(String.format("==================刷新access_token结束================%s", Tools.getNowDateTime()));

    }


    /**
     * 刷新百度accesstoken，有效期30天
     * */
    @Async
    @Scheduled(fixedRate = 2592000000l)
    public void refreshBaiduAccessToken() {

        logger.info(String.format("==================刷新baidu_access_token开始================%s", Tools.getNowDateTime()));
       baiduTools.getAccessToken();
        logger.info(String.format("==================刷新baidu_access_token结束================%s", Tools.getNowDateTime()));

    }

    /***
     * 还款提醒
     * 0 0 11 0 0 ? 客户要求上午11点
     */
    @Async
    @Scheduled(cron = "0 0 11 * * ?")
    public void sendRepayTips() {

        ReqTfData reqdata = new ReqTfData();



        RespTfData resp = dubboServiceTools.getResult("1109", reqdata);

        //logger.info("还款提醒信息=================="+resp);

        logger.info(String.format("==================发送还款提醒开始"+TEMPLATE_REPAY+"================%s", Tools.getNowDateTime()));
        if ("0000".equals(resp.getRespCode())) {
            Map map = resp.getParmMap();
            List<Map> list = (List<Map>) map.get("result");
            Map msgMap = null;
            for (Map temp : list) {
                try {

                    msgMap = new HashMap();
                    String payDate=(String)temp.get("payDate");
                    String payMoney=String.valueOf(temp.get("payMoney"));
                    String openid=(String)temp.get("openid");

                    msgMap.put("first", "尊敬的客户您好");
                    msgMap.put("headinfo", "您的贷款还款日就要到了，珍惜信用记录，请确保还款账户足额");
                    msgMap.put("payDate", payDate.substring(0,4)+"-"+payDate.substring(4,6)+"-"+payDate.substring(6,8));
                    msgMap.put("payMoney", payMoney+"元");
                    msgMap.put("remark", "美兴小贷温馨提醒：请您通过签约银行账户代扣方式还款。若因客观原因导致还款账户上未存有足够金额，而向美兴工作人员交付现金或通过支付宝、微信向美兴工作人员转账用以归还贷款的，请务必备注转账用途并向美兴工作人员索取收款收据，否则美兴小贷将无法确认您的资金用途，可能给您造成不必要的损失。");


                    if(openid==null||openid.isEmpty() ){//这段代码上线时注释掉 ||!openid.equals("o6LJ-6nsWRoiBFchtsZWIT2oiiS4")
                        continue;
                    }
                    weiChatTools.sendMsg(openid,
                            TEMPLATE_REPAY, null,
                            msgMap);

                }catch (Exception e){
                    logger.error(String.format("==================发送还款模板信息出错================%s", e.toString()));
                }
            }

        }
        logger.info(String.format("==================发送还款提醒结束================%s", Tools.getNowDateTime()));
    }

    /***
     * 逾期提醒
     * 0 0 11 0 0 ? 客户要求上午11点
     */
//    @Async
//    @Scheduled(cron = "0 0 11 * * ?")
    public void sendLoTips() {

        ReqTfData reqdata = new ReqTfData();

        RespTfData resp = dubboServiceTools.getResult("1110", reqdata);
        logger.info(String.format("==================客户逾期提醒开始"+TEMPLATE_LNLO+"================%s", Tools.getNowDateTime()));

        if ("0000".equals(resp.getRespCode())) {
            Map map = resp.getParmMap();
            List<Map> list = (List<Map>) map.get("result");
            Map msgMap = null;
            for (Map temp : list) {
                try {
                    msgMap = new HashMap();
                    String keyword1 = String.valueOf(temp.get("keyword1"));
                    String keyword2 = String.valueOf(temp.get("keyword2"));
                    String keyword3 = String.valueOf(temp.get("keyword3"));
                    String openid = (String) temp.get("openid");

                    msgMap.put("first", "尊敬的客户您好， \r\n美兴小贷温馨提醒:您有贷款已逾期，我司未能从您的约定还款账户成功扣款，为避免影响您的信用记录和产生罚息，请及时足额还款");
                    msgMap.put("keyword1", keyword1+"元");
                    msgMap.put("keyword2", keyword2.substring(0,4)+"-"+keyword2.substring(4,6)+"-"+keyword2.substring(6,8));
                    msgMap.put("keyword3", keyword3+"天");
                    msgMap.put("remark", "");

                    if (openid == null||openid.isEmpty() ) {//这段代码上线时注释掉|| !openid.equals("o6LJ-6nsWRoiBFchtsZWIT2oiiS4")
                        continue;
                    }
                    weiChatTools.sendMsg(openid,
                            TEMPLATE_LNLO, null,
                            msgMap);

                }catch (Exception e){
                    logger.error(String.format("==================发送客户逾期提醒模板信息出错================%s", e.toString()));

                }
            }
        }
        logger.info(String.format("==================客户逾期提醒结束================%s", Tools.getNowDateTime()));
    }


//    /***
//     * 发放提醒
//     *
//     */
//    @Async
//    @Scheduled(cron = "0 35 15 * * ?")
//    public void sendLoanOpenTips() {
//        logger.info(String.format("==================贷款发放通知提醒开始================%s", Tools.getNowDateTime()));
//        HashMap msgMap = new HashMap();
//        msgMap.put("first", "尊敬的客户您好， \r\n感谢您对美兴小贷的信任与支持，合同号为CONT200001020201的贷款已为您成功发放至约定账号。");
//        msgMap.put("keyword1", "88.00元");
//        msgMap.put("keyword2", "2018-03-22");
//        msgMap.put("remark", "美兴小贷温馨提醒：请您通过签约银行账户代扣方式还款。特殊情况下，向美兴工作人员交付现金或通过支付宝、微信向美兴工作人员转账用以归还贷款的，请务必备注转账用途并向美兴工作人员索取收款收据，并妥善保管收据，避免造成不必要的损失。");
//
//        weiChatTools.sendMsg("o6LJ-6nsWRoiBFchtsZWIT2oiiS4",
//                "p-kQE5XR_tJO5g83vV9t50OiICNNLjaRG56QpBDfZ6Y", null,
//                msgMap);
//        logger.info(String.format("==================贷款发放通知提醒结束================%s", Tools.getNowDateTime()));
//    }


//    /***
//     * 完结提醒
//     *
//     */
//    @Async
//    @Scheduled(cron = "0 40 15 * * ?")
//    public void sendPayoffTips() {
//        logger.info(String.format("==================贷款结清提醒开始================%s", Tools.getNowDateTime()));
//        HashMap msgMap = new HashMap();
//        msgMap.put("first", "尊敬的客户您好，\r\n感谢您对美兴的支持与信任，美兴小贷温馨提醒:您有贷款已结清。如有续贷需求，请咨询客户经理或在公众号内提交续贷申请。");
//        msgMap.put("keyword1", "CONT20200101000001");
//        msgMap.put("keyword2", "2019-03-22");
//        msgMap.put("remark", "");
//
//        weiChatTools.sendMsg("o6LJ-6nsWRoiBFchtsZWIT2oiiS4",
//                "M2sm7mb4hcuDprQFAdE9euTlAe75B9D1EzcX5r11m98", null,
//                msgMap);
//        logger.info(String.format("==================贷款结清提醒结束================%s", Tools.getNowDateTime()));
//    }

    /***
     * 优惠券到期提醒
     * 0 0 10 0 0 ? 客户要求上午10点
     */
    @Async
    @Scheduled(cron = "0 30 10 * * ?")
    public void sendCouponLoTips() {

        ReqTfData reqdata = new ReqTfData();


        RespTfData resp = dubboServiceTools.getResult("1250", reqdata);

        logger.info(String.format("==================发送优惠券到期提醒开始"+TEMPLATE_REPAY+"================%s", Tools.getNowDateTime()));
        if ("0000".equals(resp.getRespCode())) {
            Map map = resp.getParmMap();
            List<Map> list = (List<Map>) map.get("result");
            Map msgMap = null;
            for (Map temp : list) {
                try {

                    msgMap = new HashMap();
                    String mtel=(String)temp.get("mtel");
                    String validEnd=String.valueOf(temp.get("validEnd"));
                    String openid = (String) temp.get("openid");

                    msgMap.put("first", "尊敬的客户您好， \r\n您好！您的美兴充值即将到期。");
                    msgMap.put("keyword1", mtel);
                    msgMap.put("keyword2", validEnd.substring(0,4)+"-"+validEnd.substring(4,6)+"-"+validEnd.substring(6,8));
                    msgMap.put("keyword3", "充值成功，未使用。");
                    msgMap.put("remark", "点击查看详情。");


                    if(openid==null||openid.isEmpty() ){//这段代码上线时注释掉 ||!openid.equals("o6LJ-6nsWRoiBFchtsZWIT2oiiS4")
                        continue;
                    }

                    String lastUrl= null;
                    try {
                        lastUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="+APP_ID+"&redirect_uri="+ URLEncoder.encode(COUPONURL,"UTF-8")+"&response_type=code&scope=snsapi_base#wechat_redirect";
                    } catch (UnsupportedEncodingException e) {
                        e.printStackTrace();
                    }

                    weiChatTools.sendMsg(openid,
                            TEMPLATE_COUPONLO, lastUrl,
                            msgMap);

                }catch (Exception e){
                    logger.error(String.format("==================发送优惠券到期提醒模板信息出错================%s", e.toString()));
                }
            }

        }
        logger.info(String.format("==================发送优惠券到期提醒结束================%s", Tools.getNowDateTime()));
    }


}
