package com.tfrunning.gw.service;

import com.tfrunning.gw.utils.RedisTools;
import com.tfrunning.gw.utils.ThreadVariable;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by chenshunhua on 2022/9/21.
 */
@Service
public class DistributeRedisLock{


    private final static Log logger = LogFactory.getLog(DistributeRedisLock.class);

    /**
     * 分布式锁前缀
     */
    public final static String PREFIXLOCK="DistributeLock:";

    /**
     * 默认锁持有时间(单位:秒)
     */
    public final static int DEFAULT_LOCK_TIME = 300;


    @Autowired
    RedisTools redisTools;


    public boolean tryLock(String key,int seconds) {

        String lockKey = generateKey(key);

        if(redisTools.setNX(lockKey,seconds))
        {
            logger.info("获取锁：" +key);
            ThreadVariable.put(key, "1");
            return true;
        }
        logger.info("获取锁失败：" +key);
        return false;
    }

    public void releaseLock(String key) {

        logger.info("释放锁：" +key);
        if(ThreadVariable.get(key) != null){
            ThreadVariable.remove(key);
            redisTools.del(generateKey(key));
        }

    }


    private String generateKey(String key){
        return PREFIXLOCK+key;
    }

}
