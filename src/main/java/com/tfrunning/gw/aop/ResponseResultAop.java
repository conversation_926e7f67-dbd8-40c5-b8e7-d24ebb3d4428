package com.tfrunning.gw.aop;


import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import tf.mcs.service.RespTfData;

import java.lang.reflect.Method;
import java.util.Random;

/**
 * <p>
 * Controller Aop
 * 获取响应结果信息
 * </p>
 *
 * <AUTHOR>
 * @date 2023/12/25
// */
@Aspect
@Component
public class ResponseResultAop {

    private static Logger logger = LoggerFactory.getLogger(ResponseResultAop.class);


    @Pointcut("execution(public * com.tfrunning.gw.controller.PadController.*(..))"+
            "||execution(public * com.tfrunning.gw.controller.WebController.*(..))")
    public void paramPointCut(){}




    @Around("paramPointCut()")
    public Object doAround(ProceedingJoinPoint joinPoint) throws Throwable {


        logger.info("doAround");

        Object result=null;
        try {

            result = joinPoint.proceed();

        } catch (Exception e) {

            Signature signature = joinPoint.getSignature();
            // 获取真实的方法对象
            MethodSignature methodSignature = (MethodSignature) signature;
            Method method = methodSignature.getMethod();

            Class<?> returnType=method.getReturnType();

            logger.info("returnType:"+returnType);

            Random random = new Random();
            int randomNumbertmp = random.nextInt(999999);

            int randomNumber=-randomNumbertmp;

            String formattedNumber = String.format("%06d", randomNumber);

            logger.error("处理异常（"+formattedNumber+"）",e);

            if(RespTfData.class.getName().equals(returnType.getName()))
            {
                Object target = joinPoint.getTarget();
                String name = target.getClass().getName(); // 获取全类名

                RespTfData respdata=new RespTfData();
                respdata.setRespCode("9999");

                if(name.endsWith("PadController"))
                {
                    respdata.setRespDesc("网络异常，请稍后重试或联系系统管理员！("+formattedNumber+")");
                }else if(name.endsWith("WebController"))
                {
                    respdata.setRespDesc("网络异常，请稍后重试或联系客户经理！("+formattedNumber+")");
                }

                return respdata;
            }
        }
        return result;
    }


    public static void main(String[] args) {

        Random random = new Random();
        int randomNumbertmp = random.nextInt(999999);

        int randomNumber=-randomNumbertmp;

        String formattedNumber = String.format("%06d", randomNumber);


        System.out.println(formattedNumber);



    }





}
