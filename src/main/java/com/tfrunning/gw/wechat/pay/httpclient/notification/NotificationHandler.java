package com.tfrunning.gw.wechat.pay.httpclient.notification;


import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.tfrunning.gw.controller.WechatController;
import com.tfrunning.gw.wechat.pay.httpclient.auth.Verifier;
import com.tfrunning.gw.wechat.pay.httpclient.exception.ParseException;
import com.tfrunning.gw.wechat.pay.httpclient.exception.ValidationException;
import com.tfrunning.gw.wechat.pay.httpclient.notification.Notification.Resource;
import com.tfrunning.gw.wechat.pay.httpclient.util.AesUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;

/**
 * <AUTHOR>
 */
public class NotificationHandler {

    private static Logger logger = LoggerFactory.getLogger(NotificationHandler.class);

    private final Verifier verifier;
    private final byte[] apiV3Key;
    private static final ObjectMapper objectMapper = new ObjectMapper();

public NotificationHandler(Verifier verifier, byte[] apiV3Key) {
    if (verifier == null) {
        throw new IllegalArgumentException("verifier为空");
    }
    if (apiV3Key == null || apiV3Key.length == 0) {
        throw new IllegalArgumentException("apiV3Key为空");
    }
    this.verifier = verifier;
    this.apiV3Key = apiV3Key;
}

    /**
     * 解析微信支付通知请求结果
     *
     * @param request 微信支付通知请求
     * @return 微信支付通知报文解密结果
     * @throws ValidationException 1.输入参数不合法 2.参数被篡改导致验签失败 3.请求和验证的平台证书不一致导致验签失败
     * @throws ParseException 1.解析请求体为Json失败 2.请求体无对应参数 3.AES解密失败
     */
    public Notification parse(Request request)
            throws ValidationException, ParseException {
        // 验签
        validate(request);
        // 解析请求体
        return parseBody(request.getBody());
    }

    private void validate(Request request) throws ValidationException {
        if (request == null) {
            throw new ValidationException("request为空");
        }
        String serialNumber = request.getSerialNumber();
        byte[] message = request.getMessage();
        String signature = request.getSignature();
        if (serialNumber == null || serialNumber.isEmpty()) {
            throw new ValidationException("serialNumber为空");
        }
        if (message == null || message.length == 0) {
            throw new ValidationException("message为空");
        }
        if (signature == null || signature.isEmpty()) {
            throw new ValidationException("signature为空");
        }
        if (!verifier.verify(serialNumber, message, signature)) {
            String errorMessage = String
                    .format("验签失败：serial=[%s] message=[%s] sign=[%s]", serialNumber, new String(message), signature);
            throw new ValidationException(errorMessage);
        }
    }

    /**
     * 解析请求体
     *
     * @param body 请求体
     * @return 解析结果
     * @throws ParseException 解析body失败
     */
    private Notification parseBody(String body) throws ParseException {
//        ObjectReader objectReader = objectMapper.reader();
//        Notification notification;
        Notification notification = JSON.parseObject(body, Notification.class);
        logger.info("body转notification："+JSON.toJSONString(notification));
        logger.info("body转notification中resource："+JSON.toJSONString(notification.getResource()));
//        try {
//            notification = objectReader.readValue(body);
//        } catch (IOException ioException) {
//            throw new ParseException("解析body失败，body:" + body, ioException);
//        }
        validateNotification(notification);
        setDecryptData(notification);
        return notification;
    }

    public static void main(String[] args) {
        String body = "{\"id\":\"900c47e6-c3a6-57a9-a956-b4fd2bc99925\",\"create_time\":\"2022-09-16T18:49:24+08:00\",\"resource_type\":\"encrypt-resource\",\"event_type\":\"TRANSACTION.SUCCESS\",\"summary\":\"支付成功\",\"resource\":{\"original_type\":\"transaction\",\"algorithm\":\"AEAD_AES_256_GCM\",\"ciphertext\":\"PatMftfjiQcgixNboclSpQkOy6TRDrUhWuL6zSqN/+zq0kl9YspUd52i0Z3Cui7cNiua4/w6jAHmQayRn/dU2N5rlKu0L66XTCFqf046v16E2UXw9mRfdp9ob8k4YU/WbcUOb8Ey3ZcsJ3snpSD8ocRUZIPSITHo3nJQ4B448KpTIYOI3LD0JWBgOvCwB6NnxIvkUsagnLoqU8XchQmivy9ZbvzxK4dd/ubr47FykBLji0r+twqtJRaqOsqKybSs7Ba75v7aKnIuRMyzJckU6L+u2FHvoySmbKNrsrU30EYr9JgO8RciruafVCUb7Qo8vNYdJe29X4iHwiBZXfJTpgQukwLcmmBe+mX6gN6zozaS/LWIAN/ifnV8AysEh9vYZZSiZ0uXGvRIzcU3cEBTG1eWGWCoROjVMMtzkYuCMefdYOF3zP7xppG4iS/EDp4j8ch22jwp+XctlyS7J+EDieEn4lE2LXOnd8Dv2/afPS1lKIDnxPVq5PuyidXeDFflWYIqFQpOp+vPRsdNc2JRUbJxlHJ9a46nXXPu97snjsA1atVrSzD8Y0S0TAyS6kCksteae7fjjHYcrzjZe/AyunRZibL2V99mbStOQDEMyHSiyjzHSH1EPQKCt1CDixXNdJnyysSeIDQC1omU9AiocUj8leM306qDkg9aXE5AiJxYDnZ1b7Qn0UwIgbmK8Ij/mwtk8MR6DRnjJjBy4gbPESgzMq5U7Tz8aEA8VVs7\",\"associated_data\":\"transaction\",\"nonce\":\"kLnbE4YSHbpi\"}}";
        Notification notification = JSON.parseObject(body, Notification.class);
        System.out.println(1);
    }

    /**
     * 校验解析后的通知结果
     *
     * @param notification 通知结果
     * @throws ParseException 参数不合法
     */
    private void validateNotification(Notification notification) throws ParseException {
        if (notification == null) {
            throw new ParseException("body解析为空");
        }
        String id = notification.getId();
        if (id == null || id.isEmpty()) {
            throw new ParseException("body不合法，id为空。body：" + notification.toString());
        }
        String createTime = notification.getCreateTime();
        if (createTime == null || createTime.isEmpty()) {
            throw new ParseException("body不合法，createTime为空。body：" + notification.toString());
        }
        String eventType = notification.getEventType();
        if (eventType == null || eventType.isEmpty()) {
            throw new ParseException("body不合法，eventType为空。body：" + notification.toString());
        }
        String summary = notification.getSummary();
        if (summary == null || summary.isEmpty()) {
            throw new ParseException("body不合法，summary为空。body：" + notification.toString());
        }
        String resourceType = notification.getResourceType();
        if (resourceType == null || resourceType.isEmpty()) {
            throw new ParseException("body不合法，resourceType为空。body：" + notification.toString());
        }
        Resource resource = notification.getResource();
        if (resource == null) {
            throw new ParseException("body不合法，resource为空。notification：" + notification.toString());
        }
        String algorithm = resource.getAlgorithm();
        if (algorithm == null || algorithm.isEmpty()) {
            throw new ParseException("body不合法，algorithm为空。body：" + notification.toString());
        }
        String originalType = resource.getOriginalType();
        if (originalType == null || originalType.isEmpty()) {
            throw new ParseException("body不合法，original_type为空。body：" + notification.toString());
        }
        String ciphertext = resource.getCiphertext();
        if (ciphertext == null || ciphertext.isEmpty()) {
            throw new ParseException("body不合法，ciphertext为空。body：" + notification.toString());
        }
        String nonce = resource.getNonce();
        if (nonce == null || nonce.isEmpty()) {
            throw new ParseException("body不合法，nonce为空。body：" + notification.toString());
        }
    }

    /**
     * 获取解密数据
     *
     * @param notification 解析body得到的通知结果
     * @throws ParseException 解析body失败
     */
    private void setDecryptData(Notification notification) throws ParseException {

        Resource resource = notification.getResource();
        String getAssociateddData = "";
        if (resource.getAssociatedData() != null) {
            getAssociateddData = resource.getAssociatedData();
        }
        byte[] associatedData = getAssociateddData.getBytes(StandardCharsets.UTF_8);
        byte[] nonce = resource.getNonce().getBytes(StandardCharsets.UTF_8);
        String ciphertext = resource.getCiphertext();
        AesUtil aesUtil = new AesUtil(apiV3Key);
        String decryptData;
        try {
            decryptData = aesUtil.decryptToString(associatedData, nonce, ciphertext);
        } catch (GeneralSecurityException e) {
            throw new ParseException("AES解密失败，resource：" + resource.toString(), e);
        }
        notification.setDecryptData(decryptData);
    }

}
