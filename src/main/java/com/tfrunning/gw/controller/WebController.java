package com.tfrunning.gw.controller;

import com.alibaba.fastjson.JSON;
import com.github.wxpay.sdk.WXPayUtil;
import com.tfrunning.gw.utils.*;
import com.tfrunning.gw.wechat.pay.httpclient.auth.PrivateKeySigner;
import com.tfrunning.gw.wechat.pay.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.impl.client.CloseableHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.GetObjectRequest;
import software.amazon.awssdk.services.s3.model.GetObjectResponse;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import software.amazon.awssdk.services.s3.model.PutObjectResponse;
import tf.core.utils.hb.model.Pager;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * H5页面公众号
 */
@RestController
@Slf4j
@RequestMapping(value="/wechat")
public class WebController {

    private static Logger logger = LoggerFactory.getLogger(PadController.class);

    @Autowired
    private DubboServiceTools dubboServiceTools;

    private CloseableHttpClient httpClient;

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    RedisTools redisTools;

    @Autowired
    BaiduTools baiduTools;

    @Autowired
    S3Client s3Client;

    @Autowired
    private FileUpload fileUpload;

    @Autowired
    private PadController padController;

    @Autowired
    Environment environment;

    @Value("${media.s3.bucket}")
    private String bucketName;

    @RequestMapping("/service")
    @ResponseBody
    public RespTfData service(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");

        String openid = DESEncrypt.decrypt(opname);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);//未解密的openid
        reqdata.setReqEntity("wechatid", sername);//token

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * 获取ip地址
     * @return
     */
    @RequestMapping("/location")
    @ResponseBody
    public RespTfData location(
            HttpServletRequest request,
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String openid = (String) hMap.get("opname");

        openid = DESEncrypt.decrypt(openid);
        reqdata.setReqEntity("custId", openid);

        String ip = Tools.getClientIp(request);
        reqdata.setReqEntity("location", ip);
        logger.info("网络ip地址=================="+ip);

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    @RequestMapping("/pager")
    @ResponseBody
    public RespTfData pager(
            HttpServletRequest request,
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String openid = (String) hMap.get("opname");

        openid = DESEncrypt.decrypt(openid);
        reqdata.setReqEntity("custId", openid);

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);
        //分页
        Integer page = (Integer) hMap.get("page");
        Integer pageSize = (Integer) hMap.get("pageSize");
        Pager pager = new Pager(page, pageSize);
        reqdata.setReqEntity("pager", pager);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * jsAPI下单生成支付prepay_id
     * @param hMap
     * @return
     */
    @RequestMapping(value = "/getPrepayIdForMobile")
    public RespTfData getPrepayIdForMobile(@RequestBody final HashMap hMap) {
        RespTfData orResponse = new RespTfData();
        try {
            //判断是否在跑日中
            ReqTfData reqdata = new ReqTfData();
            RespTfData resp = dubboServiceTools.getResult("1572", reqdata);
            String isSystemEod=(String) resp.getRespEntity("isSystemEod");
            if(resp.getRespCode().equals("7777")){
                return resp;
            }
            if("1".equals(isSystemEod)){
                orResponse.setRespCode("9999");
                orResponse.setRespDesc("当前系统正在入账，不允许还款操作，请稍后重试");
                return orResponse;
            }
            String instcode = (String) hMap.get("balInstcode");
//            instcode="102555";
            String data = this.getPrepayIdData(hMap, instcode);
            String prepayId = padController.doJsapiHandle(data, instcode);
            Map<String, Object> reqWeixinData = new HashMap<>();
            String appid = "";
            String privateKey = "";
            String merchantSerialNumber = "";
            if ("101".equals(instcode.substring(0, 3))) {
                appid = QRCodeUtils.APPID;
                privateKey = QRCodeUtils.privateKey;
                merchantSerialNumber = QRCodeUtils.merchantSerialNumber;
            } else {
                appid = QRCodeUtils.APPID_s;
                privateKey = QRCodeUtils.privateKey_s;
                merchantSerialNumber = QRCodeUtils.merchantSerialNumber_s;
            }
            reqWeixinData.put("appId", appid);
            reqWeixinData.put("timeStamp", String.valueOf(WXPayUtil.getCurrentTimestamp()));
            reqWeixinData.put("nonceStr", WXPayUtil.generateNonceStr());
            reqWeixinData.put("package", "prepay_id=" + prepayId);
            reqWeixinData.put("signType", "RSA");
            String signMsg = WXPayUtil.getSignMsg(reqWeixinData);
            logger.info("signMsg===="+signMsg);
            PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(privateKey);
            PrivateKeySigner privateKeySigner = new PrivateKeySigner(merchantSerialNumber, merchantPrivateKey);
            String paySign = privateKeySigner.sign(signMsg.getBytes(StandardCharsets.UTF_8)).getSign();
            logger.info("paySign===="+paySign);
            reqWeixinData.put("paySign", paySign);
            orResponse.setParmMap(reqWeixinData);
            orResponse.setRespCode("0000");
            orResponse.setRespDesc("Jsapi支付生预支付成功");
        } catch (Exception e) {
            logger.error("Jsapi支付生预支付失败", e);
            orResponse.setRespCode("9999");
            orResponse.setRespDesc(e.getMessage());
        }
        return orResponse;
    }

    public String getPrepayIdData(HashMap hMap, String instcode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        if ("101".equals(instcode.substring(0, 3))) {
            map.put("appid", QRCodeUtils.APPID);
            map.put("mchid", QRCodeUtils.merchantId);
            map.put("notify_url", environment.getProperty("native.notify_url"));
        } else {
            map.put("appid", QRCodeUtils.APPID_s);
            map.put("mchid", QRCodeUtils.merchantId_s);
            map.put("notify_url", environment.getProperty("native.notify_url_s"));
        }
        map.put("description", "微信充值扣款");
        map.put("out_trade_no", padController.getOutTradeNo());
        map.put("payer", this.getOpenidJson(hMap));
        hMap.remove("openid");
        hMap.remove("token");
        hMap.remove("balInstcode");
        hMap.remove("opname");
        hMap.remove("sername");
        map.put("attach", JSON.toJSONString(hMap));
        map.put("amount", padController.getAmountJson(hMap));

        //需要的参数data
        String data = JSON.toJSONString(map);
        log.info("Jsapi生成预支付的data:"+data);
        return data;
    }

    private Map<String, Object> getOpenidJson(Map<String, Object> params) throws Exception {
        if(params==null || params.size()==0){
            throw new Exception("生成支付没有传入参数");
        }
        String opname = (String) params.get("opname");
        if(opname==null){
            throw new Exception("生成支付，没有传入必须的opname");
        }
        Map<String, Object> operate = new HashMap<>();
        String openid = DESEncrypt.decrypt(opname);
        log.info("Jsapi生成预支付的解析opname之后的openid:"+openid);
//        openid = "oVyFps-gRYpE0JL7Hs8Bcfb18vXg";
        operate.put("openid", openid);
        return operate;
    }

    /**
     * 生成微信支付链接
     * @param hMap
     * @return
     */
    @RequestMapping(value = "/nativePayLinkForMobile")
    public RespTfData nativePayLinkForMobile(@RequestBody final HashMap hMap) {
        RespTfData orResponse = new RespTfData();
        try {
            //判断是否在跑日中
            ReqTfData reqdata = new ReqTfData();
            RespTfData resp = dubboServiceTools.getResult("1572", reqdata);
            String isSystemEod=(String) resp.getRespEntity("isSystemEod");
            if(resp.getRespCode().equals("7777")){
                return resp;
            }
            if("1".equals(isSystemEod)){
                orResponse.setRespCode("9999");
                orResponse.setRespDesc("当前系统正在入账，不允许还款操作，请稍后重试");
                return orResponse;
            }
            //生成二维码需要的参数data
            String instcode = (String) hMap.get("balInstcode");
            String data = padController.getNativeQRcodeData(hMap, instcode);
            String qrCodeBase64 = padController.doQRCodeHandle(data, instcode);
            orResponse.setRespEntity("qrCodeBase64", qrCodeBase64);
            orResponse.setRespEntity("overTime", padController.getOverTime());
            orResponse.setRespCode("0000");
            orResponse.setRespDesc("Native支付生成二维码成功");
        } catch (Exception e) {
            logger.error("Native支付生成二维码失败", e);
            orResponse.setRespCode("9999");
            orResponse.setRespDesc(e.getMessage());
        }
        return orResponse;
    }

    /**
     * 获取js-sdk签名（废弃）
     * */
    @RequestMapping("/getTimestamp")
    @ResponseBody
    public RespTfData getTimestamp(
            @RequestBody final HashMap hMap
    ) {

        String url = (String) hMap.get("url");
        String jsapi_ticket = redisTools.get("jsapi_ticket");

        Map<String, String> ret = new HashMap<String, String>();
        String nonce_str = create_nonce_str();
        String timestamp = create_timestamp();
        String string1;
        String signature = "";

//        if( !url.endsWith("%2F")){
//            url=url+"%2F";
//        }
        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapi_ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url;
        logger.info("jsapi_ticket=================="+jsapi_ticket);
        logger.info("nonce_str=================="+nonce_str);
        logger.info("timestamp=================="+timestamp);
        logger.info("url=================="+url);
        try
        {
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        }
        catch (NoSuchAlgorithmException e)
        {
            e.printStackTrace();
        }
        catch (UnsupportedEncodingException e)
        {
            e.printStackTrace();
        }
        logger.info("signature=================="+signature);

        RespTfData resp = new RespTfData();
        resp.setRespEntity("signature",signature);
        resp.setRespEntity("timestamp",timestamp);
        resp.setRespEntity("noncestr",nonce_str);
        resp.setRespCode("0000");


//        String url = (String) hMap.get("url");
//        String uuid32 = Tools.get32String();
//        String timestamp = String.valueOf(System.currentTimeMillis() / 1000);
//        String jsapi_ticket = redisTools.get("jsapi_ticket");
//        String noncestr = uuid32.substring(0,16);
//
//        String str = "jsapi_ticket="+jsapi_ticket+"&noncestr="+noncestr+"&timestamp="+timestamp+"&url="+url;
//        String signature = Tools.SHA1(str);

//        RespTfData resp = new RespTfData();
//        resp.setRespEntity("signature",signature);
//        resp.setRespEntity("timestamp",timestamp);
//        resp.setRespEntity("noncestr",noncestr);
//        resp.setRespCode("0000");

        return resp;

    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash)
        {
            formatter.format("%02x", b);
        }
        String result = formatter.toString();
        formatter.close();
        return result;
    }
    private static String create_nonce_str() {
        return UUID.randomUUID().toString();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }

    @RequestMapping("/approving")
    @ResponseBody
    public RespTfData approving(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String openid = (String) hMap.get("opname");

        openid = DESEncrypt.decrypt(openid);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        //获取客户信息
        RespTfData userInfo = weiChatTools.getUserInfo(openid);
        String nickname=(String)userInfo.getRespEntity("nickname");

        reqdata.setReqEntity("nickname",nickname);
        Tools.dealWithData(reqdata,hMap);

        RespTfData respdata = dubboServiceTools.getResult("1049", reqdata);

        if("0000".equals(respdata.getRespCode())) {
            String cifid = (String) respdata.getRespEntity("cifid");
            redisTools.hset(openid,"cifid",cifid,Tools.EXPIRE_WEB_TOKEN);
            RespTfData resp = new RespTfData();
            resp.setRespCode(respdata.getRespCode());
            resp.setRespDesc(respdata.getRespDesc());
            resp.setSerid(respdata.getSerid());
            resp.setRespEntity("status",respdata.getRespEntity("status"));
            resp.setRespEntity("newUser",respdata.getRespEntity("newUser"));
            return resp;
        }else{
            return respdata;
        }
    }

    /**
     * 获取openid，cifid
     * */
    @RequestMapping("/getOp")
    @ResponseBody
    public RespTfData getOpenid(
            @RequestBody final HashMap hMap
    ) {
        String code = (String) hMap.get("code");
        RespTfData resp = weiChatTools.getOpenid(code);
        if("0000".equals(resp.getRespCode())){
            ReqTfData reqdata = new ReqTfData();
            String openid = (String) resp.getRespEntity("openid");
            openid = DESEncrypt.decrypt(openid);
            reqdata.setReqEntity("custId",openid);
            reqdata.setReqEntity("bankid", Tools.BANKID);

            RespTfData respdata = dubboServiceTools.getResult("1050", reqdata);

            if("0000".equals(respdata.getRespCode())) {
                String cifid = (String) respdata.getRespEntity("cifid");
                if(!Tools.isNull(cifid)){
                    redisTools.hset(openid,"cifid",cifid,Tools.EXPIRE_WEB_TOKEN);
                    String cliname = (String) respdata.getRespEntity("cliname");
                    String mtel = (String) respdata.getRespEntity("mtel");

                    //2024-07-01 yk 朋友推荐-多返回客户编号，方便前端对比客户是否扫描自己的二维码或链接
                    resp.setRespEntity("cifid", cifid);
                    resp.setRespEntity("cliname",cliname);
                    resp.setRespEntity("mtel",Tools.hideTel(mtel));
                }
                //直将token和openid返回前台，不返回cifid
                return resp;
            }else{
                return respdata;
            }

        }else{
            return resp;
        }

//        //以下是测试时候用的
//        RespTfData resp=new RespTfData();
//        resp.setRespCode("0000");
//        resp.setRespDesc("获取微信授权成功！");
//        ReqTfData reqdata = new ReqTfData();
//        String openid = (String) hMap.get("code");
////        openid = DESEncrypt.decrypt(openid);
//        reqdata.setReqEntity("custId",openid);
//        reqdata.setReqEntity("bankid", Tools.BANKID);
//        resp.setRespEntity("openid",DESEncrypt.encrypt(openid));
//
//        String token = redisTools.hget(openid,"token");
//        if(Tools.isNull(token)){
//            token = String.valueOf(idWorker.nextId());
//        }
//        //以openid为key可避免数据冗余，并且一个openid只保证有一个token有效
//        redisTools.hset(openid,"token",token,Tools.EXPIRE_WEB_TOKEN);
//        resp.setRespEntity("token",token);
//
//        RespTfData respdata = dubboServiceTools.getResult("1050", reqdata);
//
//        if("0000".equals(respdata.getRespCode())) {
//            String cifid = (String) respdata.getRespEntity("cifid");
//            if(!Tools.isNull(cifid)){
//                redisTools.hset(openid,"cifid",cifid,Tools.EXPIRE_WEB_TOKEN);
//                String cliname = (String) respdata.getRespEntity("cliname");
//                String mtel = (String) respdata.getRespEntity("mtel");
//                resp.setRespEntity("cliname",cliname);
//                resp.setRespEntity("mtel",Tools.hideTel(mtel));
//            }
//            //直将token和openid返回前台，不返回cifid
//            return resp;
//        }else{
//            return respdata;
//        }
    }

    @Value("${weichat.template.sign}")
    private String TEMPLATE;
    @Value("${weichat.signurl}")
    private String URL;

    /***
     * 推送客户签字
     * @param hMap
     * @return
     */
    @RequestMapping("/pushMsg")
    @ResponseBody
    public RespTfData pushMsg(
            @RequestBody final HashMap hMap
    ) {

        //向后台发送请求
        ReqTfData reqdata = new ReqTfData();

        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");

        String openid = DESEncrypt.decrypt(opname);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);//未解密的openid
        reqdata.setReqEntity("wechatid", sername);//token

        String cifid = redisTools.hget(openid, "cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata, hMap);

        RespTfData resp = dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
        if ("0000".equals(resp.getRespCode())) {
            Map map = resp.getParmMap();
            List<Map> list = (List<Map>) map.get("result");
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            sdf.format(new Date());
            Map msgMap = null;
            for (Map temp : list) {

                msgMap = new HashMap();
                msgMap.put("first", temp.get("first"));
                msgMap.put("keyword1", temp.get("keyword1"));
                msgMap.put("keyword2", temp.get("keyword2"));
                msgMap.put("keyword3", temp.get("keyword3"));
                msgMap.put("keyword4", temp.get("keyword4"));
                msgMap.put("keyword5", sdf.format(new Date()));
                msgMap.put("remark", temp.get("remark"));

                weiChatTools.sendMsg((String) temp.get("custId"),
                        TEMPLATE, URL,
                        msgMap);
            }

        }
        return resp;
    }
    /**
     * 根据openid获取授权信息
     * */
    @RequestMapping("/getAuthor")
    @ResponseBody
    public RespTfData getAuthor(
            @RequestBody final HashMap hMap
    ) {
        RespTfData respTfData = new RespTfData();

        String openid = (String) hMap.get("openid");
        String isTourist = (String) hMap.get("isTourist");//是否是游客:1-是、其他-否

        openid = DESEncrypt.decrypt(openid);
        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("custId",openid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        RespTfData respdata = dubboServiceTools.getResult("1050", reqdata);

        if("0000".equals(respdata.getRespCode())) {
            String cifid = (String) respdata.getRespEntity("cifid");
            if(!Tools.isNull(cifid)){
                redisTools.hset(openid,"cifid",cifid,Tools.EXPIRE_WEB_TOKEN);
                String cliname = (String) respdata.getRespEntity("cliname");
                String mtel = (String) respdata.getRespEntity("mtel");

                //2024-07-01 yk 朋友推荐-多返回客户编号，方便前端对比客户是否扫描自己的二维码或链接
                respTfData.setRespEntity("cifid", cifid);
                respTfData.setRespEntity("cliname",cliname);
                respTfData.setRespEntity("mtel",Tools.hideTel(mtel));
                respTfData.setRespCode("0000");
                respTfData.setRespDesc("获取微信授权成功！");
                //直将token和openid返回前台，不返回cifid
                return respTfData;
            }else{

                //2024-06-26 yk 朋友推荐-添加游客身份
                if("1".equals(isTourist)){
                    respTfData.setRespCode("0000");
                    respTfData.setRespDesc("游客身份验证成功!");
                    log.info("用户openId=" + openid + ", 以游客身份进入系统~");
                }else{
                    respTfData.setRespCode("8999");
                    respTfData.setRespDesc("您未注册，请先注册");
                }
                return respTfData;
            }
        }else{
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("您未注册，请先注册");
            return respTfData;
        }

    }

    /**
     * 上传并更新身份证有效期
     * */
    @RequestMapping("/uploadAndOcrIDCard")
    @ResponseBody
    public RespTfData uploadAndOcrIDCard(
            @RequestBody final HashMap hMap
    ) {
        ReqTfData reqdata = new ReqTfData();

        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");

        String openid = DESEncrypt.decrypt(opname);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);//未解密的openid
        reqdata.setReqEntity("wechatid", sername);//token

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        RespTfData respTfData= dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
        if(!respTfData.getRespCode().equals("0000")){
            return respTfData;
        }else{//上传成功开始OCR识别
            String imgBase64= (String) hMap.get("backpic");
            respTfData= baiduTools.getIDCardExpiringDate(imgBase64);
            if(!respTfData.getRespCode().equals("0000")){
                return respTfData;
            }else{

                String certteddate= (String)respTfData.getRespEntity("certteddate");
                return updateIDCardExpiringDate(opname,sername,certteddate);
            }
        }

    }

    /**
     * 更新身份证有效期
     * @param hMap
     * @return
     */
    @RequestMapping(value = "/checkIDCardExpiringDate", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData checkIDCardExpiringDate(
            @RequestBody final HashMap hMap
    ){

        String imgBase64= (String) hMap.get("backpic");
        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");

        RespTfData respTfData= baiduTools.getIDCardExpiringDate(imgBase64);

        if(!respTfData.getRespCode().equals("0000")){
            return respTfData;
        }else{
            String certteddate= (String)respTfData.getRespEntity("certteddate");
            return updateIDCardExpiringDate(opname,sername,certteddate);
        }
    }

    /***
     *   更新身份证有效期
     * @param encOpenid  未解密的openid
     * @param token
     * @param certteddate  身份证有效期
     * @return
     */
    public RespTfData updateIDCardExpiringDate(String encOpenid,String token, String certteddate){

        try {

            ReqTfData reqdata = new ReqTfData();
            String openid = DESEncrypt.decrypt(encOpenid);
            reqdata.setReqEntity("custId", openid);
            reqdata.setReqEntity("openid", encOpenid);//未解密的openid
            reqdata.setReqEntity("wechatid", token);//token

            String cifid = redisTools.hget(openid, "cifid");
            reqdata.setReqEntity("cifid", cifid);
            reqdata.setReqEntity("bankid", Tools.BANKID);
            reqdata.setReqEntity("certteddate", certteddate);

            return dubboServiceTools.getResult("1230", reqdata);
        }catch (Exception e){
            logger.info("更新身份证有效期失败："+e.getMessage(),e);
            RespTfData respTfData=new RespTfData();
            respTfData.setRespCode("9999");
            respTfData.setRespDesc("更新身份证有效期失败："+e.getMessage());
            return respTfData;
        }
    }

    @RequestMapping("/getGrabCouponList")
    @ResponseBody
    public RespTfData getGrabCouponList(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String openid = (String) hMap.get("opname");

        openid = DESEncrypt.decrypt(openid);
        reqdata.setReqEntity("custId", openid);

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);
        //分页
        Integer page = (Integer) hMap.get("page");
        Integer pageSize = (Integer) hMap.get("pageSize");
        Pager pager = new Pager(page, pageSize);
        reqdata.setReqEntity("pager", pager);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * H5上传文件到S3
     * @param requestMap
     * @return
     */
    @RequestMapping("/uploadFile")
    @ResponseBody
    public RespTfData uploadFile( @RequestBody final HashMap requestMap){
        RespTfData respTfData = fileUpload.uploadFile2S3(requestMap);
        RespTfData resultMap = new RespTfData();

        if("0000".equals(respTfData.getRespCode())){
            ArrayList<HashMap<String, String>> fileList =
                    (ArrayList<HashMap<String, String>>) respTfData.getRespEntity("fileList");

            if(null != fileList && !fileList.isEmpty()){
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String date = sdf.format(new Date());
                String applyNo = (String) requestMap.get("applyno");
                //String pathName = (String) requestMap.get("pathName");
                List<String> upload2S3FileUrlList = new ArrayList<>();

                for(HashMap map: fileList){
                    String filePath = (String) map.get("filePath");
                    String fileName = (String) map.get("fileName");

                    try (InputStream inputstream = new FileInputStream(filePath)){
                        byte[] fileBytes = IOUtils.toByteArray(inputstream);
                        String keyName;

                        if(!Tools.isNull(applyNo)){

                            if("statement".equalsIgnoreCase(applyNo)){
                                //流水分析(statement)/yyyyMMdd/fileName             流水分析没有客户编号、申请编号，固定传入statement
                                keyName = "MIS" + File.separator + "statement" + File.separator +
                                        date + File.separator + applyNo + File.separator + fileName;
                            }else if(applyNo.startsWith("APL")){
                                //申贷文件(AppFileaAttached)/yyyyMMdd/applyno/fileName
                                keyName = "MIS" + File.separator + "AppFileaAttached" + File.separator +
                                        date + File.separator + applyNo + File.separator + fileName;
                            }else if(applyNo.startsWith("DEF")){
                                //拖欠管理(Arrear)/yyyyMMdd/vchno/fileName      --目前没有借据号
                                keyName = "MIS" + File.separator + "Arrear" + File.separator +
                                        date + File.separator + fileName;
                            }else if("OCR".equalsIgnoreCase(applyNo)
                                    || applyNo.matches("(^P\\d*$)|^\\d+$")){//客户照片。以P开头后面是数字、或则纯数字
                                /*
                                    ·OCR-新增用户时,做ocr可能没有申请编号、客户编号，所以固定传入OCR
                                    ·客户(Cust)/yyyyMMdd/cifid/fileName      --目前没有借据号
                                 */
                                keyName = "MIS" + File.separator + "Cust" + File.separator +
                                        date + File.separator + applyNo + File.separator + fileName;
                            }else{//其余的都默认贷后文件
                                log.info("pad/upload-applyno:" + applyNo);

                                //贷后(PostLoan)/yyyyMMdd/vchno/                 --目前没有借据号
                                keyName = "MIS" + File.separator + "PostLoan" + File.separator +
                                        date + File.separator + fileName;
                            }
                        }else{//如果申请号为空，默认申贷文件
                            //申贷文件(AppFileaAttached)/yyyyMMdd/fileName
                            keyName = "MIS" + File.separator + "AppFileaAttached" + File.separator +
                                    date + File.separator + fileName;
                        }
                        PutObjectResponse putObjectResponse = s3Client.putObject(
                                PutObjectRequest.builder().bucket(bucketName).key(keyName).build(),
                                software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
                        logger.info("wechat/uploadFile-上传S3服务返回值:" + putObjectResponse);

                        //删除文件
                        File file = new File(filePath);
                        Tools.deleteDir(file);
                        map.put("filePath", keyName);
                        upload2S3FileUrlList.add(keyName);
                    } catch (Exception e) {
                        e.printStackTrace();
                        logger.error("H5文件上传S3服务器异常:", e);
                        resultMap.setRespCode("9999");
                        resultMap.setRespDesc("上传文件失败");
                        return resultMap;
                    }
                }
                String tranNo = requestMap.get("tranNo") + "";

                if(!Tools.isNull(tranNo)){
                    String opname = requestMap.get("opname") + "";
                    String openid = DESEncrypt.decrypt(opname);
                    String cifId = redisTools.hget(openid,"cifid");

                    ReqTfData reqTfData = new ReqTfData();
                    reqTfData.setReqEntity("fileList", fileList);
                    reqTfData.setReqEntity("bankid", Tools.BANKID);
                    reqTfData.setReqEntity("applyno", applyNo);
                    reqTfData.setReqEntity("cifid", cifId);
                    requestMap.remove("base64FileList");//不需要把base64文件列表传入到后MIS
                    Tools.dealWithData(reqTfData, requestMap);
                    resultMap = dubboServiceTools.getResult(tranNo , reqTfData);
                }else{
                    resultMap.setRespCode("0000");
                    resultMap.setRespDesc("上传文件成功");
                }
                resultMap.setRespEntity("urlList", upload2S3FileUrlList);
                return resultMap;
            }else{
                resultMap.setRespCode("9999");
                resultMap.setRespDesc("未获取到上传文件");
                return resultMap;
            }
        }else{
            resultMap = respTfData;
            return resultMap;
        }
    }

    /**
     * 下载文件
     * @param hMap
     * @return
     */
    @RequestMapping(value = "downloadFile", method = RequestMethod.POST)
    public RespTfData downloadFile(@RequestBody final HashMap hMap) {
        RespTfData respTfData = new RespTfData();

        try {
            String keyName = (String) hMap.get("keyName");

            if(!Tools.isNull(keyName)){
                ResponseBytes<GetObjectResponse> objectBytes = s3Client
                        .getObjectAsBytes(GetObjectRequest.builder().bucket(bucketName).key(keyName).build());
                byte[] bytes = objectBytes.asByteArray();
                respTfData.setRespEntity("base64", Base64.getMimeEncoder().encodeToString(bytes));
                respTfData.setRespCode("0000");
                respTfData.setRespDesc("下载文件Base64成功");
            }else{
                respTfData.setRespCode("9999");
                respTfData.setRespDesc("下载文件Base64失败");
                logger.error("下载文件Base64失败,参数缺失");
            }
        } catch (Exception e) {
            logger.error("下载文件Base64服务器异常", e);
            respTfData.setRespCode("7777");
            respTfData.setRespDesc("下载文件Base64失败");
        }
        return respTfData;
    }
}