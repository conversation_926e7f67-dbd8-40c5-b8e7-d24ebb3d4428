package com.tfrunning.gw.controller;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.dingtalkcontact_1_0.models.GetUserHeaders;
import com.aliyun.dingtalkoauth2_1_0.Client;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenRequest;
import com.aliyun.dingtalkoauth2_1_0.models.GetUserTokenResponse;
import com.aliyun.teaopenapi.models.Config;
import com.aliyun.teautil.models.RuntimeOptions;
import com.tfrunning.gw.utils.DubboServiceTools;
import com.tfrunning.gw.utils.TokenTools;
import com.tfrunning.gw.utils.Tools;
import com.tfrunning.gw.utils.WeiChatTools;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * pad登录授权
 */
@RestController
@Slf4j
@RequestMapping(value="/user")
public class AuthController {

    private static Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    private TokenTools tokenTools;

    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    Environment environment;

    @RequestMapping(value = "/login", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData login(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        RespTfData respdata = dubboServiceTools.getResult("0002", reqdata);

        switch (respdata.getRespCode()) {
            case "0000":
                // 请求成功
                // 生成token
                String token = tokenTools.getToken((String) hMap.get("operid"));
                //获取客户信息
                respdata = dubboServiceTools.getResult("0001", reqdata);
                respdata.setRespEntity("token",token);
                return respdata;
            default:
                return respdata;
        }
    }

    @RequestMapping(value = "/ddLogin", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData ddLogin(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        RespTfData respdata = dubboServiceTools.getResult("3080", reqdata);
        HashMap map = (HashMap) respdata.getRespEntity("map");
        ArrayList list = (ArrayList)map.get("postidList");
        String timeStamp = String.valueOf(map.get("timeStamp"));
        String nonceStr = String.valueOf(map.get("nonceStr"));
        String sign = String.valueOf(map.get("sign"));

        reqdata.setReqEntity("map",map);
        if(map!=null&&map.size()>0){
            hMap.putAll(map);
            reqdata.setReqEntity("operid",map.get("operid"));
        }
        switch (respdata.getRespCode()) {
            case "0000":
                // 请求成功
                // 生成token
                String token = tokenTools.getToken("dd-"+hMap.get("operid"));
                //获取客户信息
                respdata = dubboServiceTools.getResult("0001", reqdata);
                respdata.setRespEntity("token",token);
                respdata.setRespEntity("postidList",list);
                respdata.setRespEntity("timeStamp",timeStamp);
                respdata.setRespEntity("nonceStr",nonceStr);
                respdata.setRespEntity("sign",sign);
                return respdata;
            default:
                return respdata;
        }
    }


    public static com.aliyun.dingtalkoauth2_1_0.Client authClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkoauth2_1_0.Client(config);
    }
    /**
     * 获取用户token
     * @param hMap
     * @return
     * @throws Exception
     */
    //接口地址：注意/auth与钉钉登录与分享的回调域名地址一致
    @RequestMapping(value = "/auth", method = RequestMethod.POST)
    public RespTfData getAccessToken(@RequestBody final HashMap hMap) throws Exception {
        String authCode = (String) hMap.get("authCode");
        logger.info("pad扫码登录获取用户authCode="+authCode);
        com.aliyun.dingtalkoauth2_1_0.Client client = authClient();
        GetUserTokenRequest getUserTokenRequest = new GetUserTokenRequest()
                //应用基础信息-应用信息的AppKey,请务必替换为开发的应用AppKey
                .setClientId(environment.getProperty("dingding.AppKey"))
                //应用基础信息-应用信息的AppSecret，,请务必替换为开发的应用AppSecret
                .setClientSecret(environment.getProperty("dingding.AppSecret"))
                .setCode(authCode)
                .setGrantType("authorization_code");
        GetUserTokenResponse getUserTokenResponse = client.getUserToken(getUserTokenRequest);
        //获取用户个人token
        String accessToken = getUserTokenResponse.getBody().getAccessToken();
        logger.info("pad扫码登录获取用户accessToken="+accessToken);
        String me = getUserinfo(accessToken);
        if(StringUtils.isNotEmpty(me) && me.contains("mobile")){
            Map<String, Object> map = JSON.parseObject(me);
            String mobile = (String) map.get("mobile");
            return this.buildLoginMsg(mobile);
        }else{
            RespTfData respdata = new RespTfData();
            respdata.setRespCode("9999");
            respdata.setRespDesc("扫码登录失败！");
            return respdata;
        }
    }

    public static com.aliyun.dingtalkcontact_1_0.Client contactClient() throws Exception {
        Config config = new Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalkcontact_1_0.Client(config);
    }
    /**
     * 获取用户个人信息
     * @param accessToken
     * @return
     * @throws Exception
     */
    public String getUserinfo(String accessToken) throws Exception {
        com.aliyun.dingtalkcontact_1_0.Client client = contactClient();
        GetUserHeaders getUserHeaders = new GetUserHeaders();
        getUserHeaders.xAcsDingtalkAccessToken = accessToken;
        //获取用户个人信息，如需获取当前授权人的信息，unionId参数必须传me
        String me = JSON.toJSONString(client.getUserWithOptions("me", getUserHeaders, new RuntimeOptions()).getBody());
        logger.info("pad扫码登录获取用户个人信息me="+me);
        return me;
    }

    private RespTfData buildLoginMsg(String mobile){
        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("bankid", Tools.BANKID);
        //获取客户信息
        reqdata.setReqEntity("mobile", mobile);
        RespTfData respdata = dubboServiceTools.getResult("0001", reqdata);
        Map<String, Object> hashMap = (Map<String, Object>) respdata.getRespEntity("hashMap");
        if(hashMap==null || hashMap.get("operid")==null){
            respdata.setRespDesc("用户不存在！");
            respdata.setRespCode("9991");
            return respdata;
        }
        String operid = (String) hashMap.get("operid");
        // 生成token
        String token = tokenTools.getToken(operid);
        logger.info("扫码登录获取用户信息token:"+token);
        respdata.setRespEntity("token",token);
        return respdata;
    }


}
