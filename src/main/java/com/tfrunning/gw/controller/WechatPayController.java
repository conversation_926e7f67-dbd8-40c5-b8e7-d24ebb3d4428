package com.tfrunning.gw.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.wxpay.sdk.MyConfig;
import com.github.wxpay.sdk.WXPay;
import com.github.wxpay.sdk.WXPayConstants;
import com.github.wxpay.sdk.WXPayUtil;
import com.tfrunning.gw.service.DistributeRedisLock;
import com.tfrunning.gw.utils.*;
import com.tfrunning.gw.wechat.pay.httpclient.auth.PrivateKeySigner;
import com.tfrunning.gw.wechat.pay.httpclient.auth.Verifier;
import com.tfrunning.gw.wechat.pay.httpclient.auth.WechatPay2Credentials;
import com.tfrunning.gw.wechat.pay.httpclient.cert.CertificatesManager;
import com.tfrunning.gw.wechat.pay.httpclient.constant.WechatPayHttpHeaders;
import com.tfrunning.gw.wechat.pay.httpclient.notification.Notification;
import com.tfrunning.gw.wechat.pay.httpclient.notification.NotificationHandler;
import com.tfrunning.gw.wechat.pay.httpclient.notification.NotificationRequest;
import com.tfrunning.gw.wechat.pay.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.util.*;

/**
 * 腾讯接口
 */
@RestController
@Slf4j
@RequestMapping(value = "/wxpay")
public class WechatPayController {

    private static Logger logger = LoggerFactory.getLogger(WechatPayController.class);

    @Autowired
    RedisTools redisTools;

    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    DistributeRedisLock distributeLock;


    @Value("${weichat.pay.notify_url}")
    private String NOTIFY_URL;  //消息模板 逾期提醒
    private Verifier verifier; // 验签器
    private static CertificatesManager certificatesManager; // 平台证书管理器

    @RequestMapping(value = "/payNotifyForNative", method = RequestMethod.POST)
    public String notifyForNative(ServletRequest request, ServletResponse response) {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse= (HttpServletResponse)response;
        Map<String, Object> resultMap = new HashMap<>();
        try{
            httpServletResponse.setStatus(500);
            logger.info("进入pad微信二维码充值回调");
            String nonce=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE);
            String timestamp=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP);
            String signature=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE);
            String body=this.getBody(req);
            logger.info("进入pad微信二维码充值回调nonce:"+nonce);
            logger.info("进入pad微信二维码充值回调timestamp:"+timestamp);
            logger.info("进入pad微信二维码充值回调signature:"+signature);
            logger.info("接收到pad微信二维码充值的通知报文body:"+body);
            //验签，解析数据
            Notification notification = this.notifyHandler(nonce, timestamp, signature, body);
            this.doNotifyResult(notification, resultMap, httpServletResponse);
        }catch (Exception e){
            e.printStackTrace();
            resultMap.put("code", "FAIL");
            resultMap.put("message", "网络异常！");
        }
        return JSON.toJSONString(resultMap);
    }

    /**
     * native支付四川机构回调
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/payNotifyForNativeSC", method = RequestMethod.POST)
    public String notifyForNativeSC(ServletRequest request, ServletResponse response) {
        HttpServletRequest req = (HttpServletRequest) request;
        HttpServletResponse httpServletResponse= (HttpServletResponse)response;
        Map<String, Object> resultMap = new HashMap<>();
        try{
            httpServletResponse.setStatus(500);
            logger.info("进入pad微信二维码充值回调");
            String nonce=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_NONCE);
            String timestamp=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_TIMESTAMP);
            String signature=req.getHeader(WechatPayHttpHeaders.WECHAT_PAY_SIGNATURE);
            String body=this.getBody(req);
            logger.info("进入pad微信二维码充值回调nonce:"+nonce);
            logger.info("进入pad微信二维码充值回调timestamp:"+timestamp);
            logger.info("进入pad微信二维码充值回调signature:"+signature);
            logger.info("接收到pad微信二维码充值的通知报文body:"+body);
            //验签，解析数据
            Notification notification = this.notifyHandlerSC(nonce, timestamp, signature, body);
            this.doNotifyResult(notification, resultMap, httpServletResponse);
        }catch (Exception e){
            e.printStackTrace();
            resultMap.put("code", "FAIL");
            resultMap.put("message", "网络异常！");
        }
        return JSON.toJSONString(resultMap);
    }

    private void doNotifyResult(Notification notification, Map<String, Object> resultMap, HttpServletResponse response){

        String key="";
        try {

        String decryptData = notification.getDecryptData();
        logger.info("接收到pad微信二维码充值的通知报文，decryptData:"+ decryptData);
        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("decryptData", decryptData);

        // 新增并发控制
        JSONObject jsonObj=JSON.parseObject(decryptData);
        key=(String)jsonObj.get("out_trade_no");

        if(!distributeLock.tryLock(key,distributeLock.DEFAULT_LOCK_TIME))
        {
            resultMap.put("code", "FAIL");
            resultMap.put("message", "系统繁忙");
            return;
        }

        RespTfData resp = dubboServiceTools.getResult("1573", reqdata);
        String code=(String) resp.getRespEntity("code");
        String message=(String) resp.getRespEntity("message");
        resultMap.put("code", code);
        resultMap.put("message", message);
        if("SUCCESS".equals(code)){
            response.setStatus(200);
        }
        }catch (Exception e)
        {
            logger.error("处理失败",e);
            resultMap.put("code", "FAIL");
            resultMap.put("message", "处理失败");
        }finally {
            distributeLock.releaseLock(key);
        }

    }

    private String getBody(HttpServletRequest servletRequest) {
        //创建字符缓冲区
        StringBuilder stringBuilder = new StringBuilder();
        BufferedReader bufferedReader = null;
        InputStream inputStream = null;
        try {
            inputStream = servletRequest.getInputStream();
            if (inputStream != null) {
                bufferedReader = new BufferedReader(new InputStreamReader(inputStream));
                char[] charBuffer = new char[128];
                int bytesRead = -1;
                //将输入流里面的参数读取到字符缓冲区
                while ((bytesRead = bufferedReader.read(charBuffer)) > 0) {
                    stringBuilder.append(charBuffer, 0, bytesRead);
                }
            } else {
                stringBuilder.append("");
            }
        } catch (Exception ex) {
            logger.error("获取body参数异常", ex);
        }finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (bufferedReader != null) {
                try {
                    bufferedReader.close();
                }
                catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        //s为接口请求参数字符串类型
        return stringBuilder.toString();
    }

    private void buildNotifySetup() throws Exception {
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(QRCodeUtils.privateKey);
        // 获取证书管理器实例
        certificatesManager = CertificatesManager.getInstance();
        // 向证书管理器增加需要自动更新平台证书的商户信息
        certificatesManager.putMerchant(QRCodeUtils.merchantId, new WechatPay2Credentials(QRCodeUtils.merchantId,
                        new PrivateKeySigner(QRCodeUtils.merchantSerialNumber, merchantPrivateKey)),
                QRCodeUtils.apiV3Key.getBytes(StandardCharsets.UTF_8));
        // 从证书管理器中获取verifier
        verifier = certificatesManager.getVerifier(QRCodeUtils.merchantId);
    }

    private void buildNotifySetupSC() throws Exception {
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(QRCodeUtils.privateKey_s);
        // 获取证书管理器实例
        certificatesManager = CertificatesManager.getInstance();
        // 向证书管理器增加需要自动更新平台证书的商户信息
        certificatesManager.putMerchant(QRCodeUtils.merchantId_s, new WechatPay2Credentials(QRCodeUtils.merchantId_s,
                        new PrivateKeySigner(QRCodeUtils.merchantSerialNumber_s, merchantPrivateKey)),
                QRCodeUtils.apiV3Key_s.getBytes(StandardCharsets.UTF_8));
        // 从证书管理器中获取verifier
        verifier = certificatesManager.getVerifier(QRCodeUtils.merchantId_s);
    }

    private Notification checkNotifyData(String nonce, String timestamp, String signature, String body) throws Exception {
        // 构建request，传入必要参数
        NotificationRequest request = new NotificationRequest.Builder().withSerialNumber(QRCodeUtils.wechatPaySerial)
                .withNonce(nonce)
                .withTimestamp(timestamp)
                .withSignature(signature)
                .withBody(body)
                .build();
        NotificationHandler handler = new NotificationHandler(verifier, QRCodeUtils.apiV3Key.getBytes(StandardCharsets.UTF_8));
        // 验签和解析请求体
        Notification notification = handler.parse(request);
        if(notification==null){
            throw new Exception("pad微信native扫码支付解析数据为空");
        }
        return notification;
    }

    private Notification checkNotifyDataSC(String nonce, String timestamp, String signature, String body) throws Exception {
        // 构建request，传入必要参数
        NotificationRequest request = new NotificationRequest.Builder().withSerialNumber(QRCodeUtils.wechatPaySerial_s)
                .withNonce(nonce)
                .withTimestamp(timestamp)
                .withSignature(signature)
                .withBody(body)
                .build();
        NotificationHandler handler = new NotificationHandler(verifier, QRCodeUtils.apiV3Key_s.getBytes(StandardCharsets.UTF_8));
        // 验签和解析请求体
        Notification notification = handler.parse(request);
        if(notification==null){
            throw new Exception("pad微信native扫码支付解析数据为空");
        }
        return notification;
    }

    public Notification notifyHandler(String nonce, String timestamp, String signature, String body) throws Exception {
        this.buildNotifySetup();
        return this.checkNotifyData(nonce, timestamp, signature, body);
    }

    public Notification notifyHandlerSC(String nonce, String timestamp, String signature, String body) throws Exception {
        this.buildNotifySetupSC();
        return this.checkNotifyDataSC(nonce, timestamp, signature, body);
    }

    /**
     * 接收微信支付通知，考虑设置两个，因为是两个商户 或者先看看返回的结果中的商户号是多少，在验签
     */
    @RequestMapping(value = "/paynotify", method = RequestMethod.POST)
    public String notify(@RequestBody final String notifyData) {

        logger.info("接收到微信支付的通知报文，notifyData=[{}]", notifyData);
        try {

            Map<String, String> notifyMap = WXPayUtil.xmlToMap(notifyData);  // 转换成map

            WXPay wxpay = null;
            // 进行处理。
            // 注意特殊情况：订单已经退款，但收到了支付结果成功的通知，不应把商户侧订单状态从退款改成支付成功
            if ("SUCCESS".equals(notifyMap.get("return_code"))) {//通讯成功


                String mch_id = notifyMap.get("mch_id");//获取商户号 来决定使用哪个商户来校验签名等

                MyConfig config = new MyConfig(mch_id); //测试用同方，生产时修改为 mch_id
                wxpay = new WXPay(config,null,true); //生产
                // wxpay = new WXPay(config, null, true, true); //沙箱
                if (wxpay.isPayResultNotifySignatureValid(notifyMap)) {
                    // 签名正确
                    if ("SUCCESS".equals(notifyMap.get("result_code"))) {//交易成功

                        //还要进行其他特殊处理
                        //1.返回的订单金额是否与商户侧的订单金额一致 2.订单是否已经做成功处理  （后台提供查询接口：传入订单号，后台返回订单信息和状态）
                        //1、如果订单金额一致并且未处理，发送交易让后台进行处理
                        //2、如果金额不一致 返回错误？？？
                        //3、如果金额一致，订单已经做成功处理，直接返回成功

                        String out_trade_no=notifyMap.get("out_trade_no");//订单号
                        String total_fee=notifyMap.get("total_fee");//微信端返回的订单金额
                        ReqTfData reqdata = new ReqTfData();
                        reqdata.setReqEntity("bankid", Tools.BANKID);
                        reqdata.setReqEntity("out_trade_no", out_trade_no);
                        //reqdata.setReqEntity("reqtime", out_trade_no);
                        RespTfData resp = dubboServiceTools.getResult("1104", reqdata);

                        if("0000".equals(resp.getRespCode())) {

                            String total_fee_MCS=String.valueOf(resp.getRespEntity("total_fee"));//MCS端返回的订单金额
                            String status=(String) resp.getRespEntity("status");//MCS端返回的订单状态
                            if("1".equals(status)){//此订单后台已经成功处理
                                logger.info("微信支付交易成功，订单号为：" + notifyMap.get("out_trade_no"));
                                return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                            }else{
                                if(new BigDecimal(total_fee).compareTo(new BigDecimal(total_fee_MCS))==0){//订单未处理，而且金额校验一致，向后台发送交易通知此笔订单已成功付款
                                    RespTfData respNotify = dubboServiceTools.getResult("1105", reqdata);
                                    if("0000".equals(respNotify.getRespCode())) {
                                        logger.info("微信支付交易成功，订单号为：" + notifyMap.get("out_trade_no"));
                                        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                                    }else{
                                        return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[MCS端通知交易异常，"+respNotify.getRespDesc()+"]]></return_msg></xml>";
                                    }
                                }else{
                                    logger.info("微信支付交易失败，金额不一致，订单号为：" + notifyMap.get("out_trade_no"));
                                    return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[订单金额不一致]]></return_msg></xml>";
                                }
                            }

                        }else{
                            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[MCS端通知交易异常,"+resp.getRespDesc()+"]]></return_msg></xml>";
                        }


                    } else {
                        //交易失败out_trade_no  待考虑怎么处理
                        logger.info("微信支付交易失败，订单号为：" + notifyMap.get("out_trade_no") + ",失败原因：" + notifyMap.get("err_code_des"));
                        return "<xml><return_code><![CDATA[SUCCESS]]></return_code><return_msg><![CDATA[OK]]></return_msg></xml>";
                    }
                } else {
                    // 签名错误，如果数据里没有sign字段，也认为是签名错误
                    return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[签名错误]]></return_msg></xml>";
                }


            } else {
                //否则直接返回错误，让微信后台重新发通知
                return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[通讯异常]]></return_msg></xml>";
            }

        } catch (Exception e) {
            logger.error("处理微信支付的通知报文出错：" + e.toString());
            return "<xml><return_code><![CDATA[FAIL]]></return_code><return_msg><![CDATA[通讯异常]]></return_msg></xml>";
        }
    }

    /**
     * 统一下单
     */
    @RequestMapping(value = "/unifiedorder", method = RequestMethod.POST)
    public RespTfData unifiedorder(HttpServletRequest request, @RequestBody final HashMap hMap) {

        RespTfData respdata = new RespTfData();
        try {
            ReqTfData reqdata = new ReqTfData();

            String opname = (String) hMap.get("opname");
            String sername = (String) hMap.get("sername");

            String openid = DESEncrypt.decrypt(opname);
            reqdata.setReqEntity("custId", openid);
            reqdata.setReqEntity("openid", opname);//未解密的openid
            reqdata.setReqEntity("wechatid", sername);//token

            String cifid = redisTools.hget(openid, "cifid");
            reqdata.setReqEntity("cifid", cifid);
            reqdata.setReqEntity("bankid", Tools.BANKID);

            Tools.dealWithData(reqdata, hMap);
            reqdata.setReqEntity("appid", weiChatTools.getAPP_ID());


            String tocurrsum= String.valueOf(reqdata.getReqEntity("tocurrsum"));
            if (tocurrsum != null && !tocurrsum.isEmpty() && !tocurrsum.equals("null")) {
                long paysum = new BigDecimal(tocurrsum).multiply(new BigDecimal(100l)).longValue();
                reqdata.setReqEntity("tocurrsum",paysum);
            }

            logger.info("微信下单-向后台发送参数：" + reqdata);
            RespTfData resp = dubboServiceTools.getResult("1103", reqdata);
            logger.info("微信下单-后台返回参数：" + resp);
            String out_trade_no="";
            String total_fee="";
            String body="";
            String mch_id="";
            if("0000".equals(resp.getRespCode())) {

                out_trade_no=(String) resp.getRespEntity("out_trade_no");
                total_fee=String.valueOf(resp.getRespEntity("total_fee"));
                body=(String) resp.getRespEntity("body");
                mch_id=(String) resp.getRespEntity("mch_id");
            }else{
                return resp;
            }

//            long paysum = 0l;
//            if (tocurrsum != null && !tocurrsum.isEmpty()) {
//                paysum = new BigDecimal(tocurrsum).multiply(new BigDecimal(100l)).longValue();
//            }
//
            String ip = Tools.getClientIp(request);
            // 向后台发送下单请求，将前台所有参数传到后台，后台返回订单号、商品描述、订单金额,后台保存此订单信息
            MyConfig config = new MyConfig(mch_id);//测试用同方，生产是修改为查询到的mch_id
            //MyConfig config = new MyConfig(mch_id);//测试用同方，生产是修改为查询到的mch_id
            WXPay wxpay = new WXPay(config,null,true); //生产
            // WXPay wxpay = new WXPay(config, null, true, true); //沙箱
            wxpay.setSignType(WXPayConstants.SignType.MD5);
            Map<String, String> data = new HashMap<String, String>();
            data.put("body", body);
            data.put("out_trade_no", out_trade_no);
            data.put("device_info", "WEB");
            data.put("fee_type", "CNY");
            data.put("total_fee", String.valueOf(new BigDecimal(total_fee).longValue()));
            data.put("spbill_create_ip", ip);
            data.put("notify_url", NOTIFY_URL);
            data.put("trade_type", "JSAPI");  // 此处指定为H5支付
            //data.put("product_id", "PRODUCT-0001");
            data.put("openid", openid);
            logger.info("统一下单，发送至微信支付的报文，response=[{}]", data);

            Map<String, String> response = wxpay.unifiedOrder(data);

            logger.info("统一下单，接收到微信支付的反馈报文，response=[{}]", response);

            if ("SUCCESS".equals(response.get("return_code"))) {
                if ("SUCCESS".equals(response.get("result_code"))) {
                    Map<String, String> reqWeixinData = new HashMap<String, String>();
                    reqWeixinData.put("appId", response.get("appid"));
                    reqWeixinData.put("timeStamp", String.valueOf(WXPayUtil.getCurrentTimestamp()));
                    reqWeixinData.put("nonceStr", WXPayUtil.generateNonceStr());
                    reqWeixinData.put("package", "prepay_id=" + response.get("prepay_id"));
                    reqWeixinData.put("signType", "MD5");
                    String paySign = WXPayUtil.generateSignature(reqWeixinData, config.getKey(), WXPayConstants.SignType.MD5);
                    reqWeixinData.put("paySign", paySign);

                    //发送授权信息给后台,成功后返回到前端
                    reqdata.setReqEntity("out_trade_no",out_trade_no);
                    reqdata.setReqEntity("payinfo",JSONObject.toJSONString(reqWeixinData));
                    RespTfData respAuth = dubboServiceTools.getResult("1106", reqdata);

                    if(!"0000".equals(respAuth.getRespCode())) {
                        return respAuth;
                    }

                    respdata.setRespCode("0000");
                    respdata.setRespDesc("下单成功");
                    respdata.setRespEntity("payinfo", reqWeixinData);
                    respdata.setRespEntity("outTradeNo", data.get("out_trade_no"));
                    return respdata;
                }else{
                    respdata.setRespCode("9999");
                    respdata.setRespDesc("下单失败：" + response.get("err_code_des"));
                    return respdata;
                }
            } else {
                respdata.setRespCode("9999");
                respdata.setRespDesc("下单失败：" + response.get("return_msg"));
                return respdata;
            }
        } catch (Exception e) {
            logger.error("统一下单发生异常："+ e.toString());
        }
        return null;
    }


    /**
     * 查询订单
     */
    @RequestMapping(value = "/orderQuery", method = RequestMethod.POST)
    public RespTfData orderQuery(HttpServletRequest request, @RequestBody final HashMap hMap) {

        RespTfData respdata = new RespTfData();
        try {
            ReqTfData reqdata = new ReqTfData();

//            String opname = (String) hMap.get("opname");
//            String openid = DESEncrypt.decrypt(opname);

            Tools.dealWithData(reqdata, hMap);
            String out_trade_no = String.valueOf(reqdata.getReqEntity("out_trade_no"));
            //向后台发送交易，查询订单信息

            ReqTfData requestdata = new ReqTfData();
            requestdata.setReqEntity("bankid", Tools.BANKID);
            requestdata.setReqEntity("out_trade_no", out_trade_no);

            RespTfData response = dubboServiceTools.getResult("1104", requestdata);
            String mch_id="";
            if("0000".equals(response.getRespCode())) {
                mch_id=(String) response.getRespEntity("mch_id");//MCS端返回的订单状态
            }else{
                return response;
            }


            MyConfig config = new MyConfig(mch_id);//测试用同方，生产是修改为查询到的mch_id
            WXPay wxpay = new WXPay(config,null,true); //生产
            //WXPay wxpay = new WXPay(config, null, true, true); //沙箱
            wxpay.setSignType(WXPayConstants.SignType.MD5);

            Map<String, String> data = new HashMap<String, String>();
            data.put("out_trade_no", out_trade_no);

            respdata.setRespCode("0000");
            respdata.setRespDesc("查询交易订单成功");

            Map<String, String> respMap = new HashMap<String, String>();
            try {

                Map<String, String> resp = wxpay.orderQuery(data);
                logger.info("微信支付查询订单，反馈报文，response=[{}]", resp);

                respMap.putAll(resp);
                String return_code = resp.get("return_code");
                if ("FAIL".equals(return_code)) {
                    String return_msg = resp.get("return_msg");
                    respMap.put("result", "9991");
                    respMap.put("resultdesc", "交易失败：" + return_msg);
                    respdata.setRespEntity("response", respMap);
                    return respdata;
                }
                String result_code = resp.get("result_code");
                if ("FAIL".equals(result_code)) {
                    String err_code_des = resp.get("err_code_des");

                    respMap.put("result", "9992");
                    respMap.put("resultdesc", "交易失败：" + err_code_des);
                    respdata.setRespEntity("response", respMap);
                    return respdata;
                }

                String trade_state = resp.get("trade_state");
                if (!"SUCCESS".equals(trade_state)) {
                    String respdec = "";
                    if ("REFUND".equals(trade_state)) {
                        respdec = "转入退款";
                    } else if ("NOTPAY".equals(trade_state)) {
                        respdec = "未支付";
                    } else if ("CLOSED".equals(trade_state)) {
                        respdec = "已关闭";
                    } else if ("REVOKED".equals(trade_state)) {
                        respdec = "已撤销";
                    } else if ("USERPAYING".equals(trade_state)) {
                        respdec = "用户支付中";
                    } else if ("PAYERROR".equals(trade_state)) {
                        respdec = "支付失败";
                    }
                    respMap.put("result", "9993");
                    respMap.put("resultdesc", "交易失败：" + respdec);
                    respdata.setRespEntity("response", respMap);
                    return respdata;
                }
                respMap.put("result", "0000");
                respMap.put("resultdesc", "交易成功");
                respdata.setRespEntity("response", respMap);
                return respdata;

            } catch (Exception e) {
                log.error("查询订单状态失败，errorinfo=[{}]", e.toString());
                respdata.setRespCode("9999");
                respdata.setRespDesc("查询订单状态失败：" + e.toString());
                return respdata;

            }
        } catch (Exception e) {

        }
        return null;
    }
}
