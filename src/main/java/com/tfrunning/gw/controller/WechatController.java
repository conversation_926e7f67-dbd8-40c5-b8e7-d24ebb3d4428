package com.tfrunning.gw.controller;

import com.tfrunning.gw.model.Image;
import com.tfrunning.gw.model.PicMessage;
import com.tfrunning.gw.model.TextMessage;
import com.tfrunning.gw.utils.*;
import com.thoughtworks.xstream.XStream;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.PropertySource;
import org.springframework.web.bind.annotation.*;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Map;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import lombok.Data;
import lombok.ToString;

import javax.servlet.http.HttpServletResponse;


/**
 * 腾讯接口
 */
@PropertySource(value ="classpath:application.properties" ,ignoreResourceNotFound = true)
@RestController
@Slf4j
@RequestMapping(value="/txsource")
public class WechatController {

    private static Logger logger = LoggerFactory.getLogger(WechatController.class);

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    EmailTools emailTools;

    @Autowired
    RedisTools redisTools;

    @Value("${weichat.tx_token}")
    private String TX_TOKEN;

    @Autowired
    private DubboServiceTools dubboServiceTools;


    @Value("${customservice.reply.hi}")
    private String reply_hi;

    @Value("${customservice.reply.1}")
    private String reply_1;

    @Value("${customservice.reply.2}")
    private String reply_2;

    @Value("${customservice.reply.3}")
    private String reply_3;

    @Value("${customservice.reply.4}")
    private String reply_4;

    @Value("${customservice.reply.5}")
    private String reply_5;

    @Value("${customservice.reply.6}")
    private String reply_6;

    @Value("${customservice.reply.7}")
    private String reply_7;

    @Value("${customservice.reply.8}")
    private String reply_8;

    @Value("${customservice.reply.9}")
    private String reply_9;

    @Value("${customservice.reply.10}")
    private String reply_10;

    @Value("${customservice.reply.11}")
    private String reply_11;

    @Value("${customservice.reply.repay}")
    private String reply_repay;

    @Value("${customservice.reply.13}")
    private String reply_13;

    @Value("${customservice.reply.14}")
    private String reply_14;

    @Value("${customservice.reply.15}")
    private String reply_15;

    @Value("${customservice.reply.16}")
    private String reply_16;

    @Value("${customservice.reply.17}")
    private String reply_17;

    @Value("${customservice.reply.18}")
    private String reply_18;

    @Value("${customservice.reply.19}")
    private String reply_19;

    @Value("${customservice.reply.20}")
    private String reply_20;

    @Value("${customservice.reply.21}")
    private String reply_21;

    @Value("${customservice.reply.22}")
    private String reply_22;

    @Value("${customservice.reply.23}")
    private String reply_23;

    @Value("${customservice.reply.24}")
    private String reply_24;

    @Value("${customservice.reply.25}")
    private String reply_25;

    @Value("${customservice.reply.26}")
    private String reply_26;


    /**
     * 确认微信服务器
     */
    @RequestMapping(value = "/shake", method = RequestMethod.GET)
    public String shake(
            @RequestParam(value = "signature", required = true) final String signature,
            @RequestParam(value = "timestamp", required = true) final String timestamp,
            @RequestParam(value = "nonce", required = true) final String nonce,
            @RequestParam(value = "echostr", required = true) final String echostr
    ){

        List list = new ArrayList();
        list.add(TX_TOKEN);
        list.add(timestamp);
        list.add(nonce);

        String sign = Tools.sign(list);
        logger.info(String.format("校验是否为微信服务器signature==%s,sign==%s",signature,sign));
        if(sign.equals(signature)){
            return echostr;
        }
        return "";
    }


    /**
     * 解析微信推送消息
     * */
    @RequestMapping(value = "/shake", method = RequestMethod.POST)
    public String rcvProcess(
            HttpServletResponse response,
            @RequestBody String receiveMsg) {
        logger.info("接收到微信的通知报文，receiveMsg=[{}]", receiveMsg);

        XmlMapper xmlMapper = new XmlMapper();
        xmlMapper.setDefaultUseWrapper(false);

        //自动忽略无法对应pojo的字段
        xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        try {

            // 获取微信异步通知结果
            WxReceiveResponseBody xml = xmlMapper.readValue(receiveMsg, WxReceiveResponseBody.class);
            String event = xml.getEvent();
            String eventKey = xml.getEventKey();
            String fromUserName = xml.getFromUserName();
            String msgType = xml.getMsgType();
            String content = xml.getContent();
            String toUserName = xml.getToUserName();
            logger.info("Event=[{}]", event);
            logger.info("EventKey=[{}]", eventKey);//扫码参数
            logger.info("FromUserName=[{}]", fromUserName);
            logger.info("MsgType=[{}]", msgType);
            logger.info("content=[{}]", content);

            String custId = null, operid = null,type = null;

            if(!Tools.isNull(eventKey)){//扫码
                if ("subscribe".equals(event)) {//关注事件
                    custId = fromUserName;
                    operid = eventKey.replaceAll("qrscene_","");
                    //第一次关注发送消息
                    sendTextMsgToUser(reply_hi,fromUserName,toUserName,response);

                }else if("SCAN".equals(event)){//扫码
                    custId = fromUserName;
                    operid = eventKey;
                }
                if(operid!=null){

                    if(operid.startsWith("Branch")){
                        operid=operid.replaceAll("Branch","");
                        type="1";//机构号
                    }else{
                        type="2";//客户经理编号
                    }

                }

                if(Tools.isNull(custId)||Tools.isNull(operid)){
                    return null;
                }

                ReqTfData reqdata = new ReqTfData();
                reqdata.setReqEntity("bankid", Tools.BANKID);
                reqdata.setReqEntity("custId",custId);
                reqdata.setReqEntity("operid",operid);
                reqdata.setReqEntity("type",type);

                RespTfData respdata = dubboServiceTools.getResult("1075", reqdata);

                logger.info("绑定客户经理返回信息=[{}]", respdata.toString());

            }else if("text".equals(msgType)){//接受普通消息
                if("我要解绑".equals(content)){
                    ReqTfData reqdata = new ReqTfData();
                    reqdata.setReqEntity("bankid", Tools.BANKID);
                    reqdata.setReqEntity("custId",fromUserName);

                    RespTfData respdata = dubboServiceTools.getResult("1081", reqdata);
                    logger.info("解绑账户返回信息=[{}]", respdata.toString());
                    sendTextMsgToUser(respdata.getRespDesc(),fromUserName,toUserName,response);
                }else  if("myopenid".equals(content)){
                    sendTextMsgToUser(fromUserName,fromUserName,toUserName,response);
                }else  /*if(content.indexOf("个人二维码")!=-1){
                    if(fromUserName.equals("oVyFps0qB6AfGrHlqYBMKJwYgHV8")){
                        emailTools.sendSimpleMail("<EMAIL>","这是一个测试","这是测试内容");
                    }
                }else*/
                if(content.contains("客户经理")||"6".equals(content)){//获取客户经理信息

                    ReqTfData reqdata = new ReqTfData();
                    reqdata.setReqEntity("custId",fromUserName);
                    reqdata.setReqEntity("bankid", Tools.BANKID);
                    RespTfData respdata = dubboServiceTools.getResult("1050", reqdata);
                    if("0000".equals(respdata.getRespCode())) {
                        String cifid = (String) respdata.getRespEntity("cifid");
                        if(Tools.isNull(cifid)){//如果客户编号为空 说明没绑定过
                            sendTextMsgToUser("您还没有专属的客户经理，请拨打服务热线：**********",fromUserName,toUserName,response);
                        }else{
                            reqdata = new ReqTfData();
                            reqdata.setReqEntity("bankid", Tools.BANKID);
                            reqdata.setReqEntity("custId",fromUserName);
                            reqdata.setReqEntity("cifid",cifid);
                            respdata = dubboServiceTools.getResult("1089", reqdata);

                            if("0000".equals(respdata.getRespCode())) {
                                Map resultMap = (Map)respdata.getRespEntity("result");
                                String opername = (String) resultMap.get("opername");
                                String tel = (String) resultMap.get("tel");
                                sendTextMsgToUser("您的专属客户经理\r\n【姓名】："+opername+"\r\n【电话】："+tel,fromUserName,toUserName,response);
                            }else{
                                sendTextMsgToUser("您还没有专属的客户经理，请拨打服务热线：**********",fromUserName,toUserName,response);
                            }

                        }

                    }else{
                        sendTextMsgToUser("您还没有专属的客户经理，请拨打服务热线：**********",fromUserName,toUserName,response);
                    }

                }else{
                    //如果客户输入过hi，你好等，而且在redis里没过期，根据客户输入数字进行回复
                    //if(redisTools.hget("customerservice",fromUserName)!=null){
                        if(content.contains("1")){//查询营业网点咨询电话, 发送图片
                            sendTextMsgToUser(reply_1,fromUserName,toUserName,response);
                        }else if(content.contains("2")){// 2.贷款申请方式
                            sendTextMsgToUser(reply_2,fromUserName,toUserName,response);
                        }else if(content.contains("3")){// 3.查询贷款办理进度
                            sendTextMsgToUser(reply_3,fromUserName,toUserName,response);
                        }else if(content.contains("4")){// 4.贷款结清
                            sendTextMsgToUser(reply_4,fromUserName,toUserName,response);
                        }else if(content.contains("5")|| content.contains("退出") || content.contains("注销") || content.contains("解除") || content.contains("解绑")|| content.contains("销户")){//5.解除绑定
                            sendTextMsgToUser(reply_5,fromUserName,toUserName,response);
                        }else if(content.contains("7")|| content.contains("扣款")|| content.contains("还款")|| content.contains("提前结清")|| content.contains("结清贷款")){// 7.7/扣款/还款/提前结清
                            sendTextMsgToUser(reply_7,fromUserName,toUserName,response);
                        }else if(content.contains("8")|| content.contains("投诉")|| content.contains("建议")){//8.8/投诉/建议
                            sendTextMsgToUser(reply_8,fromUserName,toUserName,response);
                        }else if(content.contains("结清证明")){// 13.结清证明
                            sendTextMsgToUser(reply_13,fromUserName,toUserName,response);
                        }else if(content.contains("费用")||content.contains("利率")||content.contains("收费")){// 14.费用/利率/收费
                            sendTextMsgToUser(reply_14,fromUserName,toUserName,response);
                        }else if(content.contains("推荐码")){// 15.推荐码
                            sendTextMsgToUser(reply_15,fromUserName,toUserName,response);
                        }else if(content.contains("审核时间")||content.contains("审核时长")){// 16.审核时间/审核时长
                            sendTextMsgToUser(reply_16,fromUserName,toUserName,response);
                        }else if(content.contains("业务范围")||content.contains("外省")||content.contains("四川")){// 17.业务范围/外省/四川
                            sendTextMsgToUser(reply_17,fromUserName,toUserName,response);
                        }else if(content.contains("取消贷款")){// 18.取消贷款
                            sendTextMsgToUser(reply_18,fromUserName,toUserName,response);
                        }else if(content.contains("延迟还款")||content.contains("还不起")||content.contains("没钱")||content.contains("不还")){// 19.延迟还款/还不起/没钱/不还
                            sendTextMsgToUser(reply_19,fromUserName,toUserName,response);
                        }else if(content.contains("优惠券")){// 20.优惠券
                            sendTextMsgToUser(reply_20,fromUserName,toUserName,response);
                        }else if(content.contains("记录")){// 21.记录
                            sendTextMsgToUser(reply_21,fromUserName,toUserName,response);
                        }else if(content.contains("合同")){// 22.合同
                            sendTextMsgToUser(reply_22,fromUserName,toUserName,response);
                        }else if(content.contains("中介费")){// 23.中介费
                            sendTextMsgToUser(reply_23,fromUserName,toUserName,response);
                        }else if(content.contains("绑定")){// 24.绑定
                            sendTextMsgToUser(reply_24,fromUserName,toUserName,response);
                        }else if(content.contains("贷款信息")){// 25.贷款信息
                            sendTextMsgToUser(reply_25,fromUserName,toUserName,response);
                        }else if(content.contains("余额支取")){// 26.余额支取
                            sendTextMsgToUser(reply_26,fromUserName,toUserName,response);
                        }




                        else{
                            //redisTools.hset("customerservice",fromUserName,fromUserName,60*60*2);
                            sendTextMsgToUser(reply_hi,fromUserName,toUserName,response);
                        }


                        /*else if(content.trim().equals("9")){//还款逾期的影响
                            sendTextMsgToUser(reply_9,fromUserName,toUserName,response);
                        }else if(content.trim().equals("10")){//征信不良的影响 发送图片
                            sendPicMsgToUser(reply_10,fromUserName,toUserName,response);
                        }else if(content.trim().equals("11")){//投诉建议
                            sendTextMsgToUser(reply_11,fromUserName,toUserName,response);
                        }*/
                   // }
                }

            }else  if(!"text".equals(msgType))
            {
                sendTextMsgToUser(reply_hi,fromUserName,toUserName,response);
            }


        } catch (IOException ie) {
            logger.info("报文解析错误=[{}]", ie.toString());
        }

        return null;
    }

    /**
     * 发送文本消息
     * @param msg
     * @param toUser
     */
    private void sendTextMsgToUser(String msg, String toUser, String formUser, HttpServletResponse resp) throws UnsupportedEncodingException {
        TextMessage textMsg = new TextMessage();
        textMsg.setContent(new String(msg.getBytes(),"ISO-8859-1"));
        textMsg.setToUserName(toUser);
        textMsg.setFromUserName(formUser);

        //将封装的消息转为xml
        XStream xstream = new XStream();
        xstream.alias("xml", textMsg.getClass());
        String textMessageToXml = xstream.toXML(textMsg);

        logger.info("发送的消息是-------"+textMessageToXml);
        PrintWriter out=null;
        try {
            out = resp.getWriter();
            //写入返回的response中
            out.println(textMessageToXml);
        } catch (IOException e) {
            logger.error("发送微信消息失败",e);
            e.printStackTrace();
        }finally{
            //注意关流，不然会发送失败
            out.close();
        }
    }

    /**
     * 发送图文消息
     * @param media_id
     * @param toUser
     */
    private void sendPicMsgToUser(String media_id, String toUser, String formUser, HttpServletResponse resp) throws UnsupportedEncodingException {
        PicMessage picMessage = new PicMessage();
        picMessage.setToUserName(toUser);
        picMessage.setFromUserName(formUser);

        Image image=new Image();
        image.setMediaId(media_id);

        picMessage.setImage(image);
        //将封装的消息转为xml
        XStream xstream = new XStream();
        xstream.alias("xml", picMessage.getClass());
        String textMessageToXml = xstream.toXML(picMessage);

        logger.info("发送的图文消息是-------"+textMessageToXml);
        PrintWriter out=null;
        try {
            out = resp.getWriter();
            //写入返回的response中
            out.println(textMessageToXml);
        } catch (IOException e) {
            logger.error("发送微信消息失败",e);
            e.printStackTrace();
        }finally{
            //注意关流，不然会发送失败
            out.close();
        }
    }


    @Data
    @ToString
    @JacksonXmlRootElement(localName = "xml")
    public static class WxReceiveResponseBody {
        @JacksonXmlProperty(localName = "Event")
        private String event;
        @JacksonXmlProperty(localName = "EventKey")
        private String eventKey;
        @JacksonXmlProperty(localName = "FromUserName")
        private String fromUserName;
        @JacksonXmlProperty(localName = "ToUserName")
        private String toUserName;
        @JacksonXmlProperty(localName = "MsgType")
        private String msgType;
        @JacksonXmlProperty(localName = "Content")
        private String content;
        @JacksonXmlProperty(localName = "MsgId")
        private String msgId;
    }

}
