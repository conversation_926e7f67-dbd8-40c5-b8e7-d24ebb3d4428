package com.tfrunning.gw.controller;

import com.alibaba.fastjson.JSON;
import com.tfrunning.gw.service.AsyncService;
import com.tfrunning.gw.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.util.*;

/**
 * @Description: 人脸识别认证接口
 * <AUTHOR>
 * @Date: 2023/9/14/10:29
 */
@RestController
@Slf4j
@RequestMapping(value="/face")
public class FaceAuthController {
    private static Logger logger = LoggerFactory.getLogger(FaceAuthController.class);
    @Autowired
    RedisTools redisTools;
    @Autowired
    AliYunTools aliYunTools;
    @Autowired
    private DubboServiceTools dubboServiceTools;
    @Autowired
    AsyncService asyncService;
    @Autowired
    BaiduFaceTools baiduFaceTools;

    @RequestMapping(value = "/faceAuth", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData faceAuth(@RequestBody HashMap hMap){
        ReqTfData reqdata = new ReqTfData();
        Tools.dealWithData(reqdata,hMap);
        RespTfData respdata=new RespTfData();

        //前端参数
        String applyno=(String) hMap.get("applyno");//申请编号
        String prdtNo =(String) hMap.get("prdtNo");//产品编号
        String cifid=(String) hMap.get("cifid");//客户编号
        String opname = (String) hMap.get("opname");//未解密的openid
        String openid = DESEncrypt.decrypt(opname);//解密后的openid
        String sername = (String) hMap.get("sername");//token
        List serids = (List<String>)hMap.get("serids");//人脸识别图像文件serid 列表
        Map metaInfo = (Map)hMap.get("metaInfo");//阿里人脸识别所需字段：MetaInfo环境参数
        String type=(String) hMap.get("type");//百度人脸识别所需字段：活体检测类型  normal 标准级活体视频   simple 图片
        String faceChannelType="1";//人脸渠道类型默认1 1阿里 2百度

        String failTotal ="";//失败总次数
        String sceneId = "";
        String aliUrl = "";
        String baiduSuccessUrl = "";
        String baiduFailedUrl = "";
        String returnUrl = "";
        String faceTotalNum = "";
        String faceFailNum = "";
        String faceAliFailNum = "";
        String faceBaiduFailNum = "";



        //查询可用的人脸识别认证渠道
        RespTfData faceChannelResp = dubboServiceTools.getResult("3225", reqdata);
        logger.info("申请号："+applyno+"客户号："+cifid+"的人脸识别配置信息:"+JSON.toJSONString(faceChannelResp));
        //人脸识别次数校验
        String faceTotalKey= "face:total:"+cifid;//人脸识别使用总次数
        String faceFailAliKey = "face:fail:ali:"+cifid;//阿里人脸识别使用失败总次数
        String faceFailBaiduKey = "face:fail:baidu:"+cifid;
        RespTfData checkFaceUsedResp = checkFaceUsedNum(faceTotalKey,faceFailAliKey,faceFailBaiduKey,faceChannelResp);
        if(!"0000".equals(checkFaceUsedResp.getRespCode())){
            return checkFaceUsedResp;
        }else{
            failTotal = String.valueOf(checkFaceUsedResp.getRespEntity("failTotal"));

            faceTotalNum = String.valueOf(checkFaceUsedResp.getRespEntity("faceTotalNum"));
            faceFailNum = String.valueOf(checkFaceUsedResp.getRespEntity("faceFailNum"));
            faceAliFailNum = String.valueOf(checkFaceUsedResp.getRespEntity("faceAliFailNum"));
            faceBaiduFailNum = String.valueOf(checkFaceUsedResp.getRespEntity("faceBaiduFailNum"));
        }

        //查询可用的人脸识别认证渠道
        if(!"0000".equals(faceChannelResp.getRespCode())) {
            respdata.setRespCode("9999");
            respdata.setRespDesc("无可用的人脸识别渠道，请联系管理人员处理！");
            return respdata;
        }else{
            sceneId = (String) faceChannelResp.getRespEntity("sceneId");//认证场景ID
            aliUrl = (String) faceChannelResp.getRespEntity("aliUrl");//阿里人脸识别回调地址
            baiduSuccessUrl = (String) faceChannelResp.getRespEntity("baiduSuccessUrl");//百度人脸识别成功回调地址
            baiduFailedUrl = (String) faceChannelResp.getRespEntity("baiduFailedUrl");//百度人脸识别失败回调地址
            String aliChannel = (String) faceChannelResp.getRespEntity("aliChannel");//阿里人脸渠道是否可用 0否1是
            String baiduChannel = (String) faceChannelResp.getRespEntity("baiduChannel");//百度人脸渠道是否可用 0否1是
            if("0".equals(aliChannel) && "1".equals(baiduChannel)){//只有百度可用,则切换人脸识别渠道为百度
                faceChannelType = "2";
            }else if("1".equals(aliChannel) && "1".equals(baiduChannel)){//阿里和百度渠道均可用，那么需要根据失败次数判断是否切换渠道
                String numAli = redisTools.get(faceFailAliKey)==null?"0":redisTools.get(faceFailAliKey);//阿里人脸识别使用失败次数
                logger.info("客户"+cifid+"阿里人脸识别使用失败次数："+numAli);
                if(Integer.parseInt(faceAliFailNum)<=Integer.parseInt(numAli)){//阿里人脸识别失败5次后切换到百度人脸失败
                    faceChannelType = "2";
                }
            }
        }

        //进行人脸识别认证并获取认证信息
        if("2".equals(faceChannelType)){//百度人脸识别方式
            RespTfData respBaidu = baiduFaceTools.getVerifyToken(hMap);
            if(!"0000".equals(respBaidu.getRespCode())) {
                return respBaidu;
            }
            String verifyToken = (String)respBaidu.getRespEntity("verify_token");
            String parms = "?wechatid="+sername+"&openid="+opname+"&serids="+listToString(serids)
                    +"&verify_token="+verifyToken+"&faceChannelType="+faceChannelType+
                    "&applyno="+applyno+"&cifid="+cifid + "&prdtNo=" + prdtNo;
            String successParams = baiduSuccessUrl+parms;
            successParams = EncodingUtil.encodeURIComponent(successParams);
            String failedParams = baiduFailedUrl+parms;
            failedParams = EncodingUtil.encodeURIComponent(failedParams);
            returnUrl="https://brain.baidu.com/face/print/?token="+verifyToken+
                    "&successUrl="+successParams+"&failedUrl="+failedParams;
        }else{//阿里人脸识别方式
            String outerOrderNo = "FACE"+GUIDGenerator.getTxNo27();//业务唯一标识
            String parms = "?wechatid="+sername+"&openid="+opname+"&serids="+listToString(serids)+"&faceChannelType="+faceChannelType
                    +"&OuterOrderNo="+outerOrderNo+"&applyno="+applyno+"&cifid="+cifid + "&prdtNo=" + prdtNo;
            aliUrl = aliUrl+parms;
            hMap.put("sceneId",sceneId);
            hMap.put("outerOrderNo",outerOrderNo);
            hMap.put("returnUrl",aliUrl);
            hMap.put("metaInfo",metaInfo);
            hMap.put("faceChannelType",faceChannelType);

            RespTfData respAli = aliYunTools.aliFaceAuth(hMap);
            if(!"0000".equals(respAli.getRespCode())) {
                return respAli;
            }
            returnUrl = (String)respAli.getRespEntity("certifyUrl");
        }
        respdata.setRespEntity("returnUrl",returnUrl);
        respdata.setRespEntity("failTotal",failTotal);
        respdata.setRespEntity("faceTotalNum",faceTotalNum);
        respdata.setRespEntity("faceFailNum",faceFailNum);
        respdata.setRespEntity("faceAliFailNum",faceAliFailNum);
        respdata.setRespEntity("faceBaiduFailNum",faceBaiduFailNum);
        respdata.setRespCode("0000");
        respdata.setRespDesc("获取人脸识别认证信息成功");
        return respdata;
    }

    @RequestMapping(value = "/faceResult", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData faceResult(@RequestBody HashMap hMap) {
        ReqTfData reqdata = new ReqTfData();
        RespTfData respdata=new RespTfData();
        String applyno=(String) hMap.get("applyno");//申请编号
        String cifid=(String) hMap.get("cifid");//客户编号
        String faceChannelType = (String) hMap.get("faceChannelType");;//人脸渠道类型默认1 1阿里 2百度

        Tools.dealWithData(reqdata,hMap);
        int seconds = getSecondsOfToDay();//当天剩余秒数
        String faceTotalKey= "face:total:"+cifid;//人脸识别使用总次数
        String faceFailAliKey = "face:fail:ali:"+cifid;//阿里人脸识别使用失败总次数
        String faceFailBaiduKey = "face:fail:baidu:"+cifid;

        //查询可用的人脸识别认证渠道
        RespTfData faceChannelResp = dubboServiceTools.getResult("3225", reqdata);
        if(!"0000".equals(faceChannelResp.getRespCode())) {
            respdata.setRespCode("9999");
            respdata.setRespDesc("无可用的人脸识别渠道，请联系管理人员处理！");
            return respdata;
        }
        if("2".equals(faceChannelType)) {//百度人脸识别方式
            respdata = baiduFaceTools.queryFaceResultDetail(hMap);
            if(!"0000".equals(respdata.getRespCode())){
                return respdata;
            }
            String isSuccess = (String) respdata.getRespEntity("isSuccess");
            logger.info("百度人脸识别结果标识："+isSuccess);
            if("0".equals(isSuccess)){
                if(!redisTools.exists(faceFailBaiduKey)){
                    redisTools.set(faceFailBaiduKey,"1",seconds);//新增缓存百度人脸识别失败次数
                }else{
                    redisTools.incr(faceFailBaiduKey);//百度人脸识别失败次数加一
                }
            }
        }else{
            String sceneId = (String) faceChannelResp.getRespEntity("sceneId");//认证场景ID
            hMap.put("sceneId",sceneId);
            respdata = aliYunTools.aliFaceResult(hMap);
            if(!"0000".equals(respdata.getRespCode())){
                return respdata;
            }

            String isSuccess = (String) respdata.getRespEntity("isSuccess");
            logger.info("阿里人脸识别结果标识："+isSuccess);
            if("0".equals(isSuccess)){
                if(!redisTools.exists(faceFailAliKey)){
                    redisTools.set(faceFailAliKey,"1",seconds);//新增缓存阿里人脸识别失败次数
                }else{
                    redisTools.incr(faceFailAliKey);//阿里人脸识别失败次数加一
                }
            }
        }
        if(!redisTools.exists(faceTotalKey)){
            redisTools.set(faceTotalKey,"1",seconds);//新增缓存人脸识别使用总次数
        }else{
            redisTools.incr(faceTotalKey);//人脸识别使用总次数加一
        }
        respdata.setRespEntity("faceName",faceChannelResp.getRespEntity("faceName"));
        return respdata;
    }

    /**
     * 校验人脸使用次数
     * @param faceTotalKey
     * @param faceFailAliKey
     * @param faceFailBaiduKey
     * @return
     */
    public RespTfData checkFaceUsedNum(String faceTotalKey,String faceFailAliKey,String faceFailBaiduKey,RespTfData faceChannelResp){
        RespTfData respdata=new RespTfData();
        //判断人脸失败次数
        String numTotal = redisTools.get(faceTotalKey);//人脸识别使用总次数
        String numAli = redisTools.get(faceFailAliKey)==null?"0":redisTools.get(faceFailAliKey);//阿里人脸识别使用失败次数
        String numBaidu = redisTools.get(faceFailBaiduKey)==null?"0":redisTools.get(faceFailBaiduKey);//百度人脸识别使用失败次数
        int failTotal = Integer.parseInt(numAli)+Integer.parseInt(numBaidu);//人脸识别失败总次数
        logger.info(faceTotalKey+",人脸识别使用总次数："+numTotal+",阿里人脸识别失败次数："+numAli+
                ",百度人脸识别失败次数："+numBaidu+",人脸识别失败总次数："+failTotal);

        String faceTotalNum = (String) faceChannelResp.getRespEntity("faceTotalNum");//人脸识别使用总次数
        String faceFailNum = (String) faceChannelResp.getRespEntity("faceFailNum");//人脸识别失败总次数
        String faceAliFailNum = (String) faceChannelResp.getRespEntity("faceAliFailNum");//阿里人脸识别失败总次数
        String faceBaiduFailNum = (String) faceChannelResp.getRespEntity("faceBaiduFailNum");//阿里人脸识别失败总次数

        String faceTotalNumKey = "face:totalNum";//人脸识别使用总次数
        String faceFailNumKey = "face:failNum";//人脸识别失败总次数
        String faceAliFailNumKey = "face:aliFailNum";//阿里人脸识别失败总次数
        String faceBaiduFailNumKey = "face:baiduFailNum";//阿里人脸识别失败总次数

        if(Tools.isNull(faceTotalNum)){
            faceTotalNum = Tools.isNull(redisTools.get(faceTotalNumKey))?"20":redisTools.get(faceTotalNumKey);
        }
        if(Tools.isNull(faceFailNum)){
            faceFailNum = Tools.isNull(redisTools.get(faceFailNumKey))?"10":redisTools.get(faceFailNumKey);
        }
        if(Tools.isNull(faceAliFailNum)){
            faceAliFailNum = Tools.isNull(redisTools.get(faceAliFailNumKey))?"5":redisTools.get(faceAliFailNumKey);
        }
        if(Tools.isNull(faceBaiduFailNum)){
            faceBaiduFailNum = Tools.isNull(redisTools.get(faceBaiduFailNumKey))?"5":redisTools.get(faceBaiduFailNumKey);
        }
        logger.info(faceTotalKey+",人脸识别使用总次数配置："+faceTotalNum+",阿里人脸识别失败次数配置："+faceAliFailNum+
                ",百度人脸识别失败次数配置："+faceBaiduFailNum+",人脸识别失败总次数配置："+faceFailNum);

        if(faceTotalNum.equals(numTotal)){
            respdata.setRespCode("9999");
            respdata.setRespDesc("人脸识别次数过多的，请明日再试或联系客户经理转线下签约");
            return respdata;
        }
        if(failTotal>=Integer.parseInt(faceFailNum)){
            respdata.setRespCode("9999");
            respdata.setRespDesc("人脸识别失败次数过多的，请明日再试或联系客户经理转线下签约");
            return respdata;
        }
        respdata.setRespCode("0000");
        respdata.setRespEntity("faceTotalNum",faceTotalNum);
        respdata.setRespEntity("faceFailNum",faceFailNum);
        respdata.setRespEntity("faceAliFailNum",faceAliFailNum);
        respdata.setRespEntity("faceBaiduFailNum",faceBaiduFailNum);
        respdata.setRespEntity("failTotal",failTotal);
        respdata.setRespDesc("人脸识别使用总次数："+numTotal+",阿里人脸识别失败次数："+numAli+",百度人脸识别失败次数："+numBaidu);
        return respdata;
    }

    /**
     * 当天剩余秒数
     * @return
     */
    public static int getSecondsOfToDay(){
        Calendar e24= Calendar.getInstance();
        e24.set(Calendar.HOUR_OF_DAY, 23);
        e24.set(Calendar.MINUTE, 59);
        e24.set(Calendar.SECOND, 59);
        e24.set(Calendar.MILLISECOND, 999);

        long d1 = e24.getTime().getTime();//获取当天23:59:59的时间戳,单位:毫秒
        long d2 = new Date().getTime();//当前时间的时间戳,单位:毫秒

        long sub =d1-d2;//这个是毫秒
        int seconds=(int) (sub/1000);//剩余秒数
        return seconds;
    }

    public String listToString(List list){
        StringBuilder sb = new StringBuilder();
        for(int i=0;i<list.size();i++){
            if(i<list.size()-1){
                sb.append(list.get(i));
                sb.append(",");
            }else{
                sb.append(list.get(i));
            }
        }
        return sb.toString();
    }
}
