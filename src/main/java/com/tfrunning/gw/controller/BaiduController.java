package com.tfrunning.gw.controller;

import com.tfrunning.gw.service.AsyncService;
import com.tfrunning.gw.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import java.util.HashMap;
import java.util.List;

/**
 * pad登录授权
 */
@RestController
@Slf4j
@RequestMapping(value="/baidu")
public class BaiduController {

    private static Logger logger = LoggerFactory.getLogger(BaiduController.class);

    @Autowired
    BaiduTools baiduTools;

    @Autowired
    RedisTools redisTools;

    @Autowired
    private IdWorker idWorker;

    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    AsyncService asyncService;

    @RequestMapping(value = "/getverifytoken", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData getVerifyToken(
            @RequestBody final HashMap hMap
    ){

        String type=(String) hMap.get("type");
        RespTfData respdata=new RespTfData();
        String access_token = redisTools.get("baidu_access_token");
        if(Tools.isNull(access_token)){
            respdata.setRespCode("9999");
            respdata.setRespDesc("百度access_token为空");
            return respdata;
        }else{
           String verify_token= baiduTools.getVerifyToken(access_token,type);
            if(Tools.isNull(verify_token)){
                respdata.setRespCode("9999");
                respdata.setRespDesc("获取百度verify_token为空");
                return respdata;
            }else{
                //修改一下，人脸识别改成要指定人员做人脸识别，所以先进行上传指定人员信息

               //第一步获取用户信息
                ReqTfData reqdata = new ReqTfData();

                String opname = (String) hMap.get("opname");
                String sername = (String) hMap.get("sername");
                String openid = DESEncrypt.decrypt(opname);
                reqdata.setReqEntity("custId", openid);
                reqdata.setReqEntity("openid", opname);//未解密的openid
                reqdata.setReqEntity("wechatid", sername);//token
                String cifid = redisTools.hget(openid,"cifid");
                reqdata.setReqEntity("cifid", cifid);
                reqdata.setReqEntity("bankid", Tools.BANKID);

                Tools.dealWithData(reqdata,hMap);
                RespTfData userInforespdata = dubboServiceTools.getResult("1050", reqdata);


                if("0000".equals(userInforespdata.getRespCode())) {
                    String cliname = (String) userInforespdata.getRespEntity("cliname");
                    String certno = (String) userInforespdata.getRespEntity("certno");
                    RespTfData idcardSubmitpdata = baiduTools.idcardSubmit(verify_token,cliname,certno);
                    if("0000".equals(idcardSubmitpdata.getRespCode())){
                        respdata.setRespCode("0000");
                        respdata.setRespDesc("获取百度verify_token成功");
                        respdata.setRespEntity("verify_token",verify_token);
                        return respdata;
                    }else{
                        return idcardSubmitpdata;
                    }
                }else{
                    return userInforespdata;
                }

            }
        }
    }


    @RequestMapping(value = "/queryfaceresultdetail", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData queryFaceResultDetail(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String verify_token= (String) hMap.get("verify_token");

        Tools.dealWithData(reqdata,hMap);

        RespTfData respdata=new RespTfData();
        String access_token = redisTools.get("baidu_access_token");
        if(Tools.isNull(access_token)){
            respdata.setRespCode("9999");
            respdata.setRespDesc("百度access_token为空");
            return respdata;
        }else{

            if(Tools.isNull(verify_token)){
                respdata.setRespCode("9999");
                respdata.setRespDesc("百度verify_token为空");
                return respdata;
            }else{
                respdata=baiduTools.queryFaceResultDetail(access_token,verify_token);

                if (!"0000".equals(respdata.getRespCode())) {
                    return respdata;
                }else {
                    Double score=(Double)respdata.getRespEntity("score");

                    String opname = (String) hMap.get("opname");
                    String sername = (String) hMap.get("sername");

                    String openid = DESEncrypt.decrypt(opname);
                    reqdata.setReqEntity("custId", openid);
                    reqdata.setReqEntity("openid", opname);//未解密的openid
                    reqdata.setReqEntity("wechatid", sername);//token

                    String cifid = redisTools.hget(openid,"cifid");
                    reqdata.setReqEntity("cifid", cifid);
                    reqdata.setReqEntity("bankid", Tools.BANKID);

                    Tools.dealWithData(reqdata,hMap);

                    reqdata.setReqEntity("score",score);


                    logger.info("serids类型=================="+hMap.get("serids").getClass().toString());

                    asyncService.sendFacePic(openid,verify_token,(List<String>)hMap.get("serids"));
                    asyncService.sendFaceVideo(openid, verify_token, (List<String>)hMap.get("serids"));
                    return dubboServiceTools.getResult("1235", reqdata);
                }
            }
        }
    }


    @RequestMapping(value = "/testIDMatch", method = RequestMethod.GET)
    @ResponseBody
    public RespTfData testIDMatch(
    ){


        RespTfData respdata=new RespTfData();
        respdata.setRespCode("9999");
        respdata.setRespDesc("百度access_token为空");
        respdata.setRespEntity("base64",Tools.getImgStr("D:\\滴滴电子发票.pdf"));
        return respdata;

    }


}
