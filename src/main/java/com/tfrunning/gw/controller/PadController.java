package com.tfrunning.gw.controller;

import com.alibaba.fastjson.JSON;
import com.tfrunning.gw.utils.*;
import com.tfrunning.gw.wechat.pay.httpclient.WechatPayHttpClientBuilder;
import com.tfrunning.gw.wechat.pay.httpclient.auth.PrivateKeySigner;
import com.tfrunning.gw.wechat.pay.httpclient.auth.Verifier;
import com.tfrunning.gw.wechat.pay.httpclient.auth.WechatPay2Credentials;
import com.tfrunning.gw.wechat.pay.httpclient.auth.WechatPay2Validator;
import com.tfrunning.gw.wechat.pay.httpclient.cert.CertificatesManager;
import com.tfrunning.gw.wechat.pay.httpclient.exception.HttpCodeException;
import com.tfrunning.gw.wechat.pay.httpclient.exception.NotFoundException;
import com.tfrunning.gw.wechat.pay.httpclient.util.PemUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.*;
import software.amazon.awssdk.core.ResponseBytes;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;
import software.amazon.awssdk.services.s3.presigner.S3Presigner;
import software.amazon.awssdk.utils.StringUtils;
import tf.core.utils.hb.model.Pager;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.security.GeneralSecurityException;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;

import static com.tfrunning.gw.wechat.pay.httpclient.constant.WechatPayHttpHeaders.WECHAT_PAY_SERIAL;
import static org.apache.http.HttpHeaders.ACCEPT;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.apache.http.entity.ContentType.APPLICATION_JSON;

/**
 * pad服务
 * 本地测试地址：/MicroCredGw/pad
 */
@RestController
@Slf4j
@RequestMapping(value="/pad")
public class PadController {

    private static Logger logger = LoggerFactory.getLogger(PadController.class);

    private String nonce = ""; // 请求头Wechatpay-Nonce
    private String timestamp = "";// 请求头Wechatpay-Timestamp
    private String signature = "";// 请求头Wechatpay-Signature
    private String body = ""; // 请求体
    private Verifier verifier; // 验签器
    private static CertificatesManager certificatesManager; // 平台证书管理器

    private CloseableHttpClient httpClient;

    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    private TokenTools tokenTools;

    @Autowired
    private FileUpload fileUpload;

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    BaiduTools baiduTools;

    @Value("${weichat.template.renew}")
    private String TEMPLATE;
    @Value("${weichat.url}")
    private String URL;

    @Value("${media.s3.region}")
    private String regionId;
    @Value("${media.s3.bucket}")
    private String bucketName;

    @Autowired
    IdWorker idWorker;
    @Autowired
    S3Client s3Client;
    @Autowired
    S3Presigner s3Presigner;

    @Autowired
    Environment environment;

    /**
     * 普通交易
     * @param hMap
     * @return
     */
    @RequestMapping("/service")
    @ResponseBody
    public RespTfData service(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String token = (String) hMap.get("token");
        String openid = tokenTools.getUserid(token);

        reqdata.setReqEntity("bankid", Tools.BANKID);
        reqdata.setReqEntity("operid", openid);
        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * 分页交易
     * @param hMap
     * @return
     */
    @RequestMapping("/pager")
    @ResponseBody
    public RespTfData pager(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String token = (String) hMap.get("token");
        String openid = tokenTools.getUserid(token);

        reqdata.setReqEntity("bankid", Tools.BANKID);
        reqdata.setReqEntity("operid", openid);
        //分页
        Integer page = (Integer) hMap.get("page");
        Integer pageSize = (Integer) hMap.get("pageSize");
        Pager pager = new Pager(page, pageSize);
        reqdata.setReqEntity("pager", pager);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    @RequestMapping(value = "/logout", method = RequestMethod.POST)
    @ResponseBody
    public RespTfData logout(
            @RequestBody final HashMap hMap
    ){
        String token = (String) hMap.get("token");

        tokenTools.cleanupToken(token);
        RespTfData respdata = new RespTfData();
        respdata.setRespCode("0000");
        respdata.setRespDesc("注销成功");
        return respdata;
    }

    /***
     * 下载文件Base64
     * @param hMap
     * @return
     */
    @RequestMapping(value = "downloadFile", method = RequestMethod.POST)
    public RespTfData downloadFile(@RequestBody final HashMap hMap) {
        RespTfData orResponse = new RespTfData();
        String keyName = (String) hMap.get("keyName");
        try {
            logger.info("---------------- 开始从亚马逊S3系统下载文件 ----------------");
            ResponseBytes<GetObjectResponse> objectBytes = s3Client
                    .getObjectAsBytes(GetObjectRequest.builder().bucket(bucketName).key(keyName).build());
            byte[] bytes = objectBytes.asByteArray();
            logger.info("===================== 下载文件" + keyName + "成功! =====================");
            orResponse.setRespEntity("base64", Base64.getMimeEncoder().encodeToString(bytes));
            orResponse.setRespCode("0000");
            orResponse.setRespDesc("下载文件Base64成功");
        } catch (Exception e) {
            logger.error("下载文件Base64异常", e);
            orResponse.setRespCode("9999");
            orResponse.setRespDesc("下载文件Base64失败");
        }
        return orResponse;
    }


    /***
     * 上传文件
     * @param request
     * @param response
     * @param params
     * @return
     */
    @RequestMapping("/upload")
    @ResponseBody
    public RespTfData upload(
            HttpServletRequest request,
            HttpServletResponse response,
            @RequestParam(value = "params", required=true) final String params){
        RespTfData respdata = fileUpload.ngUpload(request, response);

        if("0000".equals(respdata.getRespCode())){
            ReqTfData reqdata = new ReqTfData();
            HashMap maps= JSON.parseObject(params,HashMap.class);
            ArrayList<HashMap> fileList = (ArrayList<HashMap>) respdata.getRespEntity("fileList");

            if(fileList != null && fileList.size() > 0){
                InputStream inputstream = null;
                byte[] fileBytes = null;
                PutObjectResponse putObjectResponse = null;
                SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
                String date = sdf.format(new Date());
                String applyno = (String) maps.get("applyno");

                for(HashMap map: fileList){
                    String filePath = (String) map.get("filePath");
                    String fileName = (String) map.get("fileName");

                    try {
                        inputstream = new FileInputStream(filePath);
                        fileBytes = IOUtils.toByteArray(inputstream);
                        String keyName;

                        if(!Tools.isNull(applyno)){

                            if("statement".equalsIgnoreCase(applyno)){
                                //流水分析(statement)/yyyyMMdd/fileName             流水分析没有客户编号、申请编号，固定传入statement
                                keyName = "MIS" + File.separator + "statement" + File.separator +
                                        date + File.separator + applyno + File.separator + fileName;
                            }else if(applyno.startsWith("APL")){
                                //申贷文件(AppFileaAttached)/yyyyMMdd/applyno/fileName
                                keyName = "MIS" + File.separator + "AppFileaAttached" + File.separator +
                                        date + File.separator + applyno + File.separator + fileName;
                            }else if(applyno.startsWith("DEF")){
                                //拖欠管理(Arrear)/yyyyMMdd/vchno/fileName      --目前没有借据号
                                keyName = "MIS" + File.separator + "Arrear" + File.separator +
                                        date + File.separator + fileName;
                            }else if("OCR".equalsIgnoreCase(applyno)
                                    || applyno.matches("(^P\\d*$)|^\\d+$")){//客户照片。以P开头后面是数字、或则纯数字
                                /*
                                    ·OCR-新增用户时,做ocr可能没有申请编号、客户编号，所以固定传入OCR
                                    ·客户(Cust)/yyyyMMdd/cifid/fileName      --目前没有借据号
                                 */
                                keyName = "MIS" + File.separator + "Cust" + File.separator +
                                        date + File.separator + applyno + File.separator + fileName;
                            }else{//其余的都默认贷后文件
                                log.info("pad/upload-applyno:" + applyno);

                                //贷后(PostLoan)/yyyyMMdd/vchno/                 --目前没有借据号
                                keyName = "MIS" + File.separator + "PostLoan" + File.separator +
                                        date + File.separator + fileName;
                            }
                        }else{//如果申请号为空，默认申贷文件
                            //申贷文件(AppFileaAttached)/yyyyMMdd/fileName
                            keyName = "MIS" + File.separator + "AppFileaAttached" + File.separator +
                                    date + File.separator + fileName;
                        }
                        putObjectResponse = s3Client.putObject(
                                PutObjectRequest.builder().bucket(bucketName).key(keyName).build(),
                                software.amazon.awssdk.core.sync.RequestBody.fromBytes(fileBytes));
                        logger.info("pad/upload-上传S3服务返回值:" + putObjectResponse.toString());

                        //删除文件
                        File file = new File(filePath);
                        Tools.deleteDir(file);
                        map.put("filePath", keyName);
                    } catch (Exception e) {
                        e.printStackTrace();
                        RespTfData back = new RespTfData();
                        back.setRespCode("9999");
                        back.setRespDesc("上传失败图片");
                        return back;
                    }
                }
            }else{
                RespTfData back = new RespTfData();
                back.setRespCode("9999");
                back.setRespDesc("未获取到上传图片");
                return back;
            }
            reqdata.setReqEntity("fileList",fileList);
            String token = (String) maps.get("token");
            String openid = tokenTools.getUserid(token);
            reqdata.setReqEntity("bankid", Tools.BANKID);
            reqdata.setReqEntity("operid", openid);
            Tools.dealWithData(reqdata,maps);
            return dubboServiceTools.getResult((String) maps.get("tranNo"), reqdata);
        }else{
            return respdata;
        }
    }

    /**
     * 获取二维码
     * */
    @RequestMapping("/getqrcode")
    public RespTfData getqrcode(@RequestBody final HashMap hMap) {
        String code = (String) hMap.get("code");
        String qrtype = (String) hMap.get("qrtype");

        ReqTfData reqdata = new ReqTfData();
        reqdata.setReqEntity("qrtype",qrtype);
        if("1".equals(qrtype)){//机构
            reqdata.setReqEntity("instcode",code);
        }else if("2".equals(qrtype)){//客户经理
            reqdata.setReqEntity("operid",code);
        }
        Tools.dealWithData(reqdata,hMap);

        RespTfData respdata =  dubboServiceTools.getResult("1200", reqdata);
        if("0000".equals(respdata.getRespCode())) {
            String existsFlg = (String) respdata.getRespEntity("existsFlg");
            if("1".equals(existsFlg)){//说明当前已有二维码
                String imageUrl=(String) respdata.getRespEntity("imageUrl");
                RespTfData qs = new RespTfData();
                qs.setRespCode("0000");
                qs.setRespDesc("获取二维码成功");
                qs.setRespEntity("qr",imageUrl);
                return qs;
            }else{
                return weiChatTools.getQRcode(qrtype,code);
            }

        }else{
            return respdata;
        }
    }

    /**
     * 客户经理/机构生成二维码
     * */
    @RequestMapping("/qrgener")
    public RespTfData qrGenerators(@RequestBody final HashMap hMap) {
        String code = (String) hMap.get("code");
        String qrtype = (String) hMap.get("qrtype");

        return weiChatTools.getQRcode(qrtype,code);
    }

    /***
     * 微信推送简化续贷消息
     * @param hMap
     * @return
     */
    @RequestMapping("/pushMsg")
    @ResponseBody
    public RespTfData pushMsg(
            @RequestBody final HashMap hMap
    ){

        String openid = (String) hMap.get("openid");
        String keyword1 = (String) hMap.get("keyword1");
        String keyword2 = (String) hMap.get("keyword2");
        String keyword3 = (String) hMap.get("keyword3");
        String keyword4 = (String) hMap.get("keyword4");

        Map map = new HashMap();
        map.put("first","尊敬的美兴优质客户，您的当前贷款即将到期，" +
                "我们邀请您在微信申请自助续贷，无需到营业部签合同，最快一个工作日可放款");
        map.put("keyword1",keyword1);
        map.put("keyword2",keyword2);
        map.put("keyword3",keyword3);
        map.put("keyword4",keyword4);
        map.put("remark","点击详情，申请续贷。");

        weiChatTools.sendMsg(openid,
                TEMPLATE,URL,
                map);
        RespTfData qs = new RespTfData();
        qs.setRespCode("0000");
        qs.setRespDesc("推送成功");
        return qs;
    }

    /**
     * 修改密码，无token校验的
     * @param hMap
     * @return
     */
    @RequestMapping("/changepass")
    @ResponseBody
    public RespTfData changePassword(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();
        
        String operid = (String) hMap.get("operid");
        reqdata.setReqEntity("bankid", Tools.BANKID);
        reqdata.setReqEntity("operid", operid);
        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * 二要素验证
     * */
    @RequestMapping("/queryIDMatch")
    public RespTfData queryIDMatch(@RequestBody final HashMap hMap) {
        String cliname = (String) hMap.get("cliname");
        String certno = (String) hMap.get("certno");
        return baiduTools.queryIDMatch(certno,cliname);
    }

    /**
     * 生成微信支付链接并发送钉钉消息
     * @param hMap
     * @return
     */
    @RequestMapping(value = "/nativeQRcodeForLink")
    public RespTfData nativeQRcodeForLink(@RequestBody final HashMap hMap) {
        RespTfData orResponse = new RespTfData();
        try {
            //判断是否在跑日中
            ReqTfData reqdata = new ReqTfData();
            RespTfData resp = dubboServiceTools.getResult("1572", reqdata);
            String isSystemEod=(String) resp.getRespEntity("isSystemEod");
            if(resp.getRespCode().equals("7777")){
                return resp;
            }
            if("1".equals(isSystemEod)){
                orResponse.setRespCode("9999");
                orResponse.setRespDesc("当前系统正在入账，不允许还款操作，请稍后重试");
                return orResponse;
            }
            //生成二维码需要的参数data
            String token = (String) hMap.get("token");
            String instcode = (String) hMap.get("balInstcode");
            String data = this.getNativeQRcodeData(hMap, instcode);
            String qrUrl = this.doQRCodeHandleLink(data, instcode);
            String overTime = this.getOverTime();
            String operid = tokenTools.getUserid(token);
            reqdata.setReqEntity("operid", operid);
            reqdata.setReqEntity("qrUrl", qrUrl);
            reqdata.setReqEntity("overTime", overTime);
            reqdata.setReqEntity("vchno", hMap.get("vchno"));
            reqdata.setReqEntity("totalAmount", hMap.get("totalAmount"));
            logger.error("生成微信支付链接并发送钉钉消息operid:", operid);
            RespTfData respData = dubboServiceTools.getResult("1635", reqdata);
            return respData;
        } catch (Exception e) {
            logger.error("生成微信支付链接并发送钉钉消息失败", e);
            orResponse.setRespCode("9999");
            orResponse.setRespDesc(e.getMessage());
        }
        return orResponse;
    }

    public String getNativeQRcodeData(HashMap hMap, String instcode) throws Exception {
        Map<String, Object> map = new HashMap<>();
        if ("101".equals(instcode.substring(0, 3))) {
            map.put("appid", QRCodeUtils.APPID);
            map.put("mchid", QRCodeUtils.merchantId);
            map.put("notify_url", environment.getProperty("native.notify_url"));
        } else {
            map.put("appid", QRCodeUtils.APPID_s);
            map.put("mchid", QRCodeUtils.merchantId_s);
            map.put("notify_url", environment.getProperty("native.notify_url_s"));
        }
        map.put("description", "微信充值扣款");
        map.put("out_trade_no", this.getOutTradeNo());
        hMap.remove("token");
        hMap.remove("balInstcode");
        hMap.remove("opname");
        hMap.remove("sername");
        map.put("attach", JSON.toJSONString(hMap));
        map.put("amount", this.getAmountJson(hMap));
        //生成二维码需要的参数data
        String data = JSON.toJSONString(map);
        log.info("生成微信支付链接并发送钉钉消息data:"+data);
        return data;
    }

    /**
     * native扫码二维码南充
     * @param hMap
     * @return
     */
    @RequestMapping(value = "/nativeQRcode")
    public RespTfData nativeQRcode(@RequestBody final HashMap hMap) {
        RespTfData orResponse = new RespTfData();
        try {
            //判断是否在跑日中
            ReqTfData reqdata = new ReqTfData();
            RespTfData resp = dubboServiceTools.getResult("1572", reqdata);
            String isSystemEod=(String) resp.getRespEntity("isSystemEod");
            if(resp.getRespCode().equals("7777")){
                return resp;
            }
            if("1".equals(isSystemEod)){
                orResponse.setRespCode("9999");
                orResponse.setRespDesc("当前系统正在入账，不允许还款操作，请稍后重试");
                return orResponse;
            }
            //生成二维码需要的参数data
            String instcode = (String) hMap.get("balInstcode");
            String data = this.getNativeQRcodeData(hMap, instcode);
            String qrCodeBase64 = this.doQRCodeHandle(data, instcode);
            orResponse.setRespEntity("qrCodeBase64", qrCodeBase64);
            orResponse.setRespEntity("overTime", this.getOverTime());
            orResponse.setRespCode("0000");
            orResponse.setRespDesc("Native支付生成二维码成功");
        } catch (Exception e) {
            logger.error("Native支付生成二维码失败", e);
            orResponse.setRespCode("9999");
            orResponse.setRespDesc(e.getMessage());
        }
        return orResponse;
    }

    public String getOverTime(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR, 2);// 24小时制
        System.out.println("after:" + format.format(cal.getTime()));  //显示更新后的日期
        return format.format(cal.getTime());
    }

    public CloseableHttpResponse buildHttpResponse(String data, String instcode, String url) throws Exception {
        HttpPost httpPost = new HttpPost(url);
        if ("101".equals(instcode.substring(0, 3))) {
            this.beforeNative();
            httpPost.addHeader(WECHAT_PAY_SERIAL, QRCodeUtils.wechatPaySerial);
        } else {
            this.beforeNativeSC();
            httpPost.addHeader(WECHAT_PAY_SERIAL, QRCodeUtils.wechatPaySerial_s);
        }
        StringEntity reqEntity = new StringEntity(data, APPLICATION_JSON);
        httpPost.setEntity(reqEntity);
        httpPost.addHeader(ACCEPT, APPLICATION_JSON.toString());
        return httpClient.execute(httpPost);
    }

    public String doQRCodeHandleLink(String data, String instcode) throws Exception {
        CloseableHttpResponse response = buildHttpResponse(data, instcode, QRCodeUtils.NATIVE_URL);
        log.info("Native支付生成二维码返回response:"+JSON.toJSONString(response));
        Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity()));
        logger.info("Native支付生成二维码返回{map}：" + JSON.toJSONString(maps));
        if(response.getStatusLine().getStatusCode() == SC_UNAUTHORIZED){
            throw new Exception("401");
        }
        if(response.getStatusLine().getStatusCode() == SC_BAD_REQUEST){
            throw new Exception("400");
        }
        if(response.getStatusLine().getStatusCode() != 200){
            throw new Exception("请求二维码异常");
        }
        try {
            HttpEntity entity = response.getEntity();
            // do something useful with the response body
            // and ensure it is fully consumed
            EntityUtils.consume(entity);
            Map<String, Object> responseMap = (Map<String, Object>) maps;
            String codeUrl = (String)responseMap.get("code_url");
//			String url = "weixin://wxpay/bizpayurl/up?pr=NwY5Mz9&groupid=00";
            return codeUrl;
        } finally {
            response.close();
            this.afterNative();
        }
    }

    public String doQRCodeHandle(String data, String instcode) throws Exception {
        CloseableHttpResponse response = buildHttpResponse(data, instcode, QRCodeUtils.NATIVE_URL);
        log.info("Native支付生成二维码返回response:"+JSON.toJSONString(response));
        Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity()));
        logger.info("Native支付生成二维码返回{map}：" + JSON.toJSONString(maps));
        if(response.getStatusLine().getStatusCode() == SC_UNAUTHORIZED){
            throw new Exception("401");
        }
        if(response.getStatusLine().getStatusCode() == SC_BAD_REQUEST){
            throw new Exception("400");
        }
        if(response.getStatusLine().getStatusCode() != 200){
            throw new Exception("请求二维码异常");
        }
        try {
            HttpEntity entity = response.getEntity();
            // do something useful with the response body
            // and ensure it is fully consumed
            EntityUtils.consume(entity);
            Map<String, Object> responseMap = (Map<String, Object>) maps;
            String codeUrl = (String)responseMap.get("code_url");
//			String url = "weixin://wxpay/bizpayurl/up?pr=NwY5Mz9&groupid=00";
            return QRCodeUtils.createQRCodeAsBase64(codeUrl);
        } finally {
            response.close();
            this.afterNative();
        }
    }

    public String doJsapiHandle(String data, String instcode) throws Exception {
        CloseableHttpResponse response = buildHttpResponse(data, instcode, QRCodeUtils.JSAPI_URL);
        log.info("Jsapi支付生成预支付返回response:"+JSON.toJSONString(response));
        Map maps = (Map) JSON.parse(EntityUtils.toString(response.getEntity()));
        logger.info("Jsapi支付生成预支付返回{map}：" + JSON.toJSONString(maps));
        if(response.getStatusLine().getStatusCode() == SC_UNAUTHORIZED){
            throw new Exception("401");
        }
        if(response.getStatusLine().getStatusCode() == SC_BAD_REQUEST){
            throw new Exception("400");
        }
        if(response.getStatusLine().getStatusCode() != 200){
            throw new Exception("Jsapi支付生成预支付异常");
        }
        try {
            HttpEntity entity = response.getEntity();
            // do something useful with the response body
            // and ensure it is fully consumed
            EntityUtils.consume(entity);
            Map<String, Object> responseMap = (Map<String, Object>) maps;
            String prepayId = (String)responseMap.get("prepay_id");
            return prepayId;
        } finally {
            response.close();
            this.afterNative();
        }
    }

    private void beforeNative() throws Exception {
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(QRCodeUtils.privateKey);
        // 获取证书管理器实例
        certificatesManager = CertificatesManager.getInstance();
        // 向证书管理器增加需要自动更新平台证书的商户信息
        certificatesManager.putMerchant(QRCodeUtils.merchantId, new WechatPay2Credentials(QRCodeUtils.merchantId,
                        new PrivateKeySigner(QRCodeUtils.merchantSerialNumber, merchantPrivateKey)),
                QRCodeUtils.apiV3Key.getBytes(StandardCharsets.UTF_8));
        // 从证书管理器中获取verifier
        verifier = certificatesManager.getVerifier(QRCodeUtils.merchantId);
        httpClient = WechatPayHttpClientBuilder.create()
                .withMerchant(QRCodeUtils.merchantId, QRCodeUtils.merchantSerialNumber, merchantPrivateKey)
                .withValidator(new WechatPay2Validator(certificatesManager.getVerifier(QRCodeUtils.merchantId)))
                .build();
    }

    private void beforeNativeSC() throws Exception {
        PrivateKey merchantPrivateKey = PemUtil.loadPrivateKey(QRCodeUtils.privateKey_s);
        // 获取证书管理器实例
        certificatesManager = CertificatesManager.getInstance();
        // 向证书管理器增加需要自动更新平台证书的商户信息
        certificatesManager.putMerchant(QRCodeUtils.merchantId_s, new WechatPay2Credentials(QRCodeUtils.merchantId_s,
                        new PrivateKeySigner(QRCodeUtils.merchantSerialNumber_s, merchantPrivateKey)),
                QRCodeUtils.apiV3Key_s.getBytes(StandardCharsets.UTF_8));
        // 从证书管理器中获取verifier
        verifier = certificatesManager.getVerifier(QRCodeUtils.merchantId_s);
        httpClient = WechatPayHttpClientBuilder.create()
                .withMerchant(QRCodeUtils.merchantId_s, QRCodeUtils.merchantSerialNumber_s, merchantPrivateKey)
                .withValidator(new WechatPay2Validator(certificatesManager.getVerifier(QRCodeUtils.merchantId_s)))
                .build();
    }

    private void afterNative() throws IOException {
        httpClient.close();
    }

    /**
     * 生成订单号（yyyyMMddHHmmss+4位数的随机数）
     * @return
     */
    public String getOutTradeNo(){
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String nowDate = sdf.format(date);
        int random = (int)((Math.random()*9+1)*1000);
        return nowDate+random;
    }

    /**
     * 封装支付金额
     * @param params
     * @return
     * @throws Exception
     */
    public Map<String, Object> getAmountJson(Map<String, Object> params) throws Exception {
        if(params==null || params.size()==0){
            throw new Exception("生成支付没有传入参数");
        }
        String totalAmount = (String) params.get("totalAmount");
        if(totalAmount==null){
            throw new Exception("生成支付，没有需要支付的金额");
        }
        BigDecimal decimal = new BigDecimal(totalAmount);
        //验证服务费计算是否正确
        BigDecimal a = new BigDecimal(0.002);
        BigDecimal b = decimal.multiply(a).setScale(2, BigDecimal.ROUND_HALF_UP);
        String serviceCharge = (String) params.get("serviceCharge");
        logger.info("服务费前端计算serviceCharge："+serviceCharge+";后端计算b："+b+"");
        if(serviceCharge==null){
            throw new Exception("生成支付，没有传服务费");
        }
        BigDecimal c = new BigDecimal(serviceCharge);
        if(b.compareTo(c)!=0){
            log.info("服务费前端计算："+c+";后端计算："+b+";不相等，不能继续充值！");
            throw new Exception("生成支付失败，请刷新微信后重试");
        }
        decimal = decimal.multiply(new BigDecimal(100));//元转分
        Map<String, Object> amountMap = new HashMap<>();
        amountMap.put("total", decimal.setScale(0, BigDecimal.ROUND_UP).intValue());
        amountMap.put("currency", "CNY");
        return amountMap;
    }

    public static void main(String[] args) {
//        String totalAmount = "0.1";
//        BigDecimal decimal = new BigDecimal(totalAmount);
//        decimal = decimal.multiply(new BigDecimal(100));//元转分
//        System.out.print(decimal.setScale(0, BigDecimal.ROUND_UP).intValue());

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar cal = Calendar.getInstance();
        cal.setTime(new Date());
        cal.add(Calendar.HOUR, 2);// 24小时制
        System.out.println("after:" + format.format(cal.getTime()));  //显示更新后的日期
    }

}
