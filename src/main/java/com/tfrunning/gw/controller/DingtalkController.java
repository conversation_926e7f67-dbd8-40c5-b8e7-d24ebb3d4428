package com.tfrunning.gw.controller;

import com.tfrunning.gw.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import tf.core.utils.hb.model.Pager;
import tf.mcs.service.ReqTfData;
import tf.mcs.service.RespTfData;

import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * H5页面-钉钉
 */
@RestController
@Slf4j
@RequestMapping(value="/dingtalk")
public class DingtalkController {

    private static Logger logger = LoggerFactory.getLogger(PadController.class);


    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Autowired
    WeiChatTools weiChatTools;

    @Autowired
    RedisTools redisTools;

    @RequestMapping("/service")
    @ResponseBody
    public RespTfData service(
            @RequestBody final HashMap hMap
    ){
        ReqTfData reqdata = new ReqTfData();

        String opname = (String) hMap.get("opname");
        String sername = (String) hMap.get("sername");

        String openid = DESEncrypt.decrypt(opname);
        reqdata.setReqEntity("custId", openid);
        reqdata.setReqEntity("openid", opname);//未解密的openid
        reqdata.setReqEntity("wechatid", sername);//token

        String cifid = redisTools.hget(openid,"cifid");
        reqdata.setReqEntity("cifid", cifid);
        reqdata.setReqEntity("bankid", Tools.BANKID);

        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult((String) hMap.get("tranNo"), reqdata);
    }

    /**
     * 根据URL 获取客户经理信息
     * */
    @RequestMapping("/getInfoByUrl")
    @ResponseBody
    public RespTfData getInfoByUrl(
            @RequestBody final HashMap hMap
    ) {
        ReqTfData reqdata = new ReqTfData();
        Tools.dealWithData(reqdata,hMap);

        return dubboServiceTools.getResult("1215", reqdata);
    }

}
