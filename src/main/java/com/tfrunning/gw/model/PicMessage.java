package com.tfrunning.gw.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 文本消息类封装
 *
 */
@Setter
@Getter
public class PicMessage {

    private Image Image;
    private String MsgType;
    private Long CreateTime;
    private String ToUserName;
    private String FromUserName;

    public PicMessage() {
        this.setMsgType("image");
        this.setCreateTime(new Date().getTime());
    }



}
