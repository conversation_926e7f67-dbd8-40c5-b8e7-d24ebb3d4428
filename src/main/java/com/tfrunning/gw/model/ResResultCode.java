package com.tfrunning.gw.model;

/**
 * API统一返回状态码枚举类
 * <AUTHOR>
 * @version v1.0 2018-08-16 Dav
 * @version v1.1 2018-08-17 Dav 增加token令牌失效类型
 */
public enum ResResultCode {

    SUCCESS(1000, "执行成功"),
    FAILURE(9999, "执行失败"),
    AUTH_INVALID(8888, "token令牌失效");

    private Integer code;

    private String message;

    ResResultCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    Integer code() {
        return this.code;
    }

    String message() {
        return this.message;
    }

    String getMessage(String name) {
        for (ResResultCode item : ResResultCode.values()) {
            if (item.name().equals(name)) {
                return item.message;
            }
        }
        return null;
    }

    Integer getCode(String name) {
        for (ResResultCode item : ResResultCode.values()) {
            if (item.name().equals(name)) {
                return item.code;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return this.name();
    }
}
