package com.tfrunning.gw.model;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 文本消息类封装
 *
 */
@Setter
@Getter
public class TextMessage{

    private String Content;
    private String MsgType;
    private Long CreateTime;
    private String ToUserName;
    private String FromUserName;

    public TextMessage() {
        this.setMsgType("text");
        this.setCreateTime(new Date().getTime());
    }


}
