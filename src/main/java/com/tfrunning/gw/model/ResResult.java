package com.tfrunning.gw.model;

/**
 * 通用restful API 返回结构体
 * <AUTHOR>
 * @version v1.0 2018-08-16 Dav
 */
public class ResResult {

	private Integer code;

	private String message;

	private Object data;

	public Integer getCode() {
		return code;
	}

	public void setCode(Integer code) {
		this.code = code;
	}

	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}

	public Object getData() {
		return data;
	}

	public void setData(Object data) {
		this.data = data;
	}

	public ResResult() {

	}

	public ResResult(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	/**
	 * 通用成功返回结构体
	 * @return
	 */
	public static ResResult success() {
		ResResult resResult = new ResResult();
		resResult.setResultCode(ResResultCode.SUCCESS);
		return resResult;
	}


	/**
	 * 通用成功返回结构体含data
	 * @param data
	 * @return
	 */
	public static ResResult success(Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(ResResultCode.SUCCESS);
		resResult.setData(data);
		return resResult;
	}

	/**
	 * 通用成功返回结构体含data,丰富成功的结构体
	 * @param code
	 * @param data
	 * @return
	 * add by sssh 20181109
	 * */
	public static ResResult success(Integer code,String mess,Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(code);
		resResult.setMessage(mess);
		resResult.setData(data);

		return resResult;
	}

	/**
	 * 通用失败返回结构体
	 * @return
	 */
	public static ResResult failure() {
		ResResult resResult = new ResResult();
		resResult.setResultCode(ResResultCode.FAILURE);
		return resResult;
	}

	/**
	 * 通用失败返回结构体含全局状态码
	 * @return
	 */
	public static ResResult failure(ResResultCode name) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(name);
		return resResult;
	}

	/**
	 * 通用失败返回结构体含data
	 * @return
	 */
	public static ResResult failure(Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(ResResultCode.FAILURE);
		resResult.setData(data);
		return resResult;
	}

	/**
	 * 通用失败返回结构体含全局状态码及data
	 * @return
	 */
	public static ResResult failure(ResResultCode name, Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(name);
		resResult.setData(data);
		return resResult;
	}

	/**
	 * 通用失败返回结构体自定义状态码及描述
	 * @return
	 */
	public static ResResult failure(Integer code, String message) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(code, message);
		return resResult;
	}

	/**
	 * 通用失败返回结构体自定义状态码及描述
	 * @return
	 */
	public static ResResult failure(String code, String message) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(Integer.parseInt(code), message);
		return resResult;
	}

	/**
	 * 通用失败返回结构体自定义状态码及描述含data
	 * @return
	 */
	public static ResResult failure(Integer code, String message, Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(code, message);
		resResult.setData(data);
		return resResult;
	}

	/**
	 * token令牌失效专用 AUTH_INVALID
	 * */
	public static ResResult authINV(Object data) {
		ResResult resResult = new ResResult();
		resResult.setResultCode(ResResultCode.AUTH_INVALID);
		resResult.setData(data);
		return resResult;
	}

	/**
	 * 返回结构体全局状态码快速赋值
	 * @return
	 */
	private void setResultCode(ResResultCode name) {
		this.code = name.code();
		this.message = name.message();
	}

	/**
	 * 返回结构体自定义状态码及描述快速赋值
	 * @return
	 */
	private void setResultCode(Integer code, String message) {
		this.code = code;
		this.message = message;
	}

	/**
	 * 返回结构体自定义状态码及描述快速赋值
	 * @return
	 */
	private void setResultCode(Integer code) {
		this.code = code;
	}
}
