package com.tfrunning.gw;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ImportResource;

@SpringBootApplication
@EnableCaching
@ServletComponentScan
@ImportResource("classpath:dubbo.xml")
public class GwApplication {

    public static void main(String[] args) {
        SpringBeanUtil.setApplicationContext(SpringApplication.run(GwApplication.class, args));
    }

}
