package com.github.wxpay.sdk;

import com.github.wxpay.sdk.WXPayConfig;
import org.springframework.beans.factory.annotation.Value;


import java.io.*;

public class MyConfig extends WXPayConfig {

    private byte[] certData;

    private String mch_id = null;
    private String appid = null;
    public MyConfig(String mch_id) throws Exception {
        this.mch_id=mch_id;
//        String certPath = this.getClass().getResource("apiclient_cert.p12").getFile();;
//        File file = new File(certPath);
//        InputStream certStream = new FileInputStream(file);
//        this.certData = new byte[(int) file.length()];
//        certStream.read(this.certData);
//        certStream.close();
    }

//    public MyConfig() throws Exception {
//
//    }


    public String getAppID() {

//        return "wx95871a8051ac2741";//同方软银人
        return "wx5a8134a9d7af0674";//美兴
    }

    public String getMchID() {
        // return "1485260032";//同方软银人
        // return "1603816283";//美兴小额贷款（四川）有限责任公司
        // return "1603820235";//南充美兴小额贷款有限责任公司
        return this.mch_id;
    }

    public String getKey() {

        if(this.mch_id.equals("1485260032")){//同方软银人
            return "XgjhE2L3OSo6ydUmBW8dff15niwadgMZ"; //同方软银人 真实
            //  return "5f227906d927191c3f2bad4a20e2bdda";//同方软银人 沙箱
        }else if(this.mch_id.equals("1603816283")){//美兴小额贷款（四川）有限责任公司
            return "fSEGZocIb4uBwpILIYTVaVus4HAfLjzl"; //美兴小额贷款（四川）有限责任公司 真实

        }else if(this.mch_id.equals("1603820235")){//南充美兴小额贷款有限责任公司
            return "NOuDVbzucZGry4C7naGuQekphC8V923V"; // 南充美兴小额贷款有限责任公司 真实
        }else{
            return null;
        }
    }
    public IWXPayDomain getWXPayDomain(){
        IWXPayDomain iwxPayDomain = new IWXPayDomain() {

            public void report(String domain, long elapsedTimeMillis, Exception ex) {

            }

            public DomainInfo getDomain(WXPayConfig config) {
                return new IWXPayDomain.DomainInfo(WXPayConstants.DOMAIN_API, true);
            }
        };
        return iwxPayDomain;
    }

    public InputStream getCertStream() {
        ByteArrayInputStream certBis = new ByteArrayInputStream(this.certData);
        return certBis;
    }

    public int getHttpConnectTimeoutMs() {
        return 8000;
    }

    public int getHttpReadTimeoutMs() {
        return 10000;
    }
}