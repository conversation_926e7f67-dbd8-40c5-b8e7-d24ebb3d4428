package tf.mcs.service;

import com.alibaba.fastjson.JSONObject;
import tf.tools.security.DESEncrypt;
import tf.tools.security.SecurityUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * dubbo客户端请求参数
 * 
 * <AUTHOR>
 * @version v1.0 2016-12-2
 *          <P>
 *          v2.0 20180607miaoyunli添加token及checkToken
 */
public class ReqTfData implements Serializable {

	private static final long serialVersionUID = -2715721540290455842L;
	private String systemId;// 请求系统编号 必输
	private String serviceId;// 请求交易编号 必输
	// private String workDate;// 请求方系统日期 必输
	private Map<String, Object> parmMap = new HashMap<String, Object>();// 请求参数

	public ReqTfData() {

	}

	public ReqTfData(String systemid, String serviceid) {
		systemId = systemid;
		serviceId = serviceid;
	}

	public Map<String, Object> getParmMap() {
		return parmMap;
	}

	public void setParmMap(Map<String, Object> parmMap) {
		// 压值方式
//		parmMap.putAll(parmMap);
		this.parmMap = parmMap;
	}

	public String getServiceId() {
		return serviceId;
	}

	public void setServiceId(String serviceId) {
		this.serviceId = serviceId;
	}

	public void setReqEntity(String key, Object entity) {
		parmMap.put(key, entity);
	}

	public Object getReqEntity(String key) {
		return parmMap.get(key);
	}

	@Override
	public String toString() {
		return "ReqTfData [systemId=" + systemId + ", serviceId=" + serviceId + ", parmMap=" + parmMap + "]";
	}

	public String getSystemId() {
		return systemId;
	}

	public void setSystemId(String systemId) {
		this.systemId = systemId;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((parmMap == null) ? 0 : parmMap.hashCode());
		result = prime * result + ((serviceId == null) ? 0 : serviceId.hashCode());
		result = prime * result + ((systemId == null) ? 0 : systemId.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		ReqTfData other = (ReqTfData) obj;
		if (parmMap == null) {
			if (other.parmMap != null)
				return false;
		} else if (!parmMap.equals(other.parmMap))
			return false;
		if (serviceId == null) {
			if (other.serviceId != null)
				return false;
		} else if (!serviceId.equals(other.serviceId))
			return false;
		if (systemId == null) {
			if (other.systemId != null)
				return false;
		} else if (!systemId.equals(other.systemId))
			return false;
		return true;
	}

	/**
	 * 对请求进行token装载（采用非对称及单向散列混合加密）
	 * 
	 * <AUTHOR>
	 * @param secretKey
	 *            RSA公钥
	 * @version V1.0 miaoyunli@**********
	 * @throws Exception
	 */
	public EncryptReqTfData encrypt(String secretKey) throws Exception {
		EncryptReqTfData encryptReqTfData = new EncryptReqTfData();
		String checkToken = new Random().nextInt(999999999) + "_" + new Random().nextInt(999999999);
		String json = JSONObject.toJSONString(this);
		String token = new DESEncrypt().encrypt(json, checkToken.getBytes());
		checkToken = SecurityUtils.encryptByString(checkToken, secretKey);
		encryptReqTfData.setToken(token);
		encryptReqTfData.setCheckToken(checkToken);
		encryptReqTfData.setSystemid(systemId);
		return encryptReqTfData;
	}

}
