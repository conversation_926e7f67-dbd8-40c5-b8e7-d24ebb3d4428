package tf.mcs.service;

import java.io.Serializable;

public class EncryptReqTfData implements Serializable {

	private static final long serialVersionUID = 1029618937828220928L;

	private String token;// 校验令牌
	private String checkToken;// 密钥令牌
	private String systemid;

	public String getSystemid() {
		return systemid;
	}

	public void setSystemid(String systemid) {
		this.systemid = systemid;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	public String getCheckToken() {
		return checkToken;
	}

	public void setCheckToken(String checkToken) {
		this.checkToken = checkToken;
	}

	@Override
	public String toString() {
		return "EncryptReqTfData [token=" + token + ", checkToken=" + checkToken + ", systemid=" + systemid + "]";
	}

}
