package tf.mcs.service;

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * 飞马贷后台对外接口
 * 
 * @version V1.0 20180813 miaoyl
 */
@Component("IIfmsService")
public interface IIfmsService {

	/**
	 * 飞马贷后台统一调用方法
	 * 
	 * @param reqTfData
	 *            交易编码
	 * @version V1.0 20180813 miaoyl
	 */
	RespTfData uniformCallMethod(EncryptReqTfData reqTfData);

	RespTfData dealmedia(ReqTfData reqTfData);


}
