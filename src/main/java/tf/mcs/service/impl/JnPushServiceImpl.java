package tf.mcs.service.impl;

import com.tfrunning.gw.utils.DubboServiceTools;
import com.tfrunning.gw.utils.RedisTools;
import com.tfrunning.gw.utils.Tools;
import com.tfrunning.gw.utils.WeiChatTools;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import tf.mcs.service.JnPushService;

import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 微贷通过dubbo连接gateway，向微信公众号推送消息(未使用)
 */
public class JnPushServiceImpl implements JnPushService {

    private final static Log logger = LogFactory.getLog(JnPushServiceImpl.class);

    @Autowired
    WeiChatTools weiChatTools;
    @Autowired
    RedisTools redisTools;
    @Autowired
    private DubboServiceTools dubboServiceTools;

    @Override
    public void getPush(String openid, String template, Map map) {
        logger.info(String.format("接收消息openid：%s，模板编号template：%s", openid, template));
        logger.info(String.format("推送消息内容：%s", map.toString()));
        String miss = "{\"errmsg\":\"miss\",\"errcode\":\"9999\"}";

        if(Tools.isNull(openid)){
            miss=miss.replace("miss","推送失败，推送消息openid为空");
            weiChatTools.saveMsg(openid,template,map,miss);
            logger.info(miss);
            return;
        }

        if(Tools.isNull(template)){
            miss=miss.replace("miss","推送失败，推送模板编号template为空");
            weiChatTools.saveMsg(openid,template,map,miss);
            logger.info(miss);
            return;
        }

        if(map == null || map.size() == 0){
            miss=miss.replace("miss","推送失败，推送内容为空");
            weiChatTools.saveMsg(openid,template,map,miss);
            logger.info(miss);
            return;
        }

        weiChatTools.sendMsg(openid,template,null,map);

    }

    @Override
    public void getNoticePush(String openid, Map map) {
        weiChatTools.sendNoticeMsg(openid,map);
    }

    /**
     * 人脸识别手动切换渠道接收消息
     * @param cifid
     * @param faceChannelType
     */
    @Override
    public void changeFaceChannel(String cifid,String faceChannelType,String faceAliFailNum){
        logger.info(String.format("人脸识别手动切换渠道接收消息 客户编号：%s，渠道编号：%s", cifid, faceChannelType));
        String faceFailAliKey = "face:fail:ali:"+cifid;//阿里人脸识别使用失败总次数

        int seconds = getSecondsOfToDay();//当天剩余秒数
        if("2".equals(faceChannelType)) {//切换到百度人脸识别  那么只需将阿里的失败次数修改>5 即可
            int aliFailNum = Integer.parseInt(faceAliFailNum) +1;
            redisTools.set(faceFailAliKey,String.valueOf(aliFailNum),seconds);//新增缓存阿里人脸识别失败次数
        }else{//切换到阿里人脸识别  那么只需将阿里的失败次数修改<5 即可
            redisTools.set(faceFailAliKey,"0",seconds);
        }
    }

    /**
     * 获取客户阿里人脸识别的使用失败次数
     * @param cifid
     * @return
     */
    @Override
    public String getAliFaceFailNum(String cifid) {
        logger.info(String.format("获取客户阿里人脸识别的使用失败次数接收消息 客户编号：%s", cifid));
        String faceFailAliKey = "face:fail:ali:"+cifid;//阿里人脸识别使用失败总次数
        String numAli = redisTools.get(faceFailAliKey)==null?"0":redisTools.get(faceFailAliKey);//阿里人脸识别使用失败次数
        logger.info("获取客户阿里人脸识别的使用失败次数---客户"+cifid+"失败次数："+numAli);
        return numAli;
    }

    /**
     * 当天剩余秒数
     * @return
     */
    public static int getSecondsOfToDay(){
        Calendar e24= Calendar.getInstance();
        e24.set(Calendar.HOUR_OF_DAY, 23);
        e24.set(Calendar.MINUTE, 59);
        e24.set(Calendar.SECOND, 59);
        e24.set(Calendar.MILLISECOND, 999);

        long d1 = e24.getTime().getTime();//获取当天23:59:59的时间戳,单位:毫秒
        long d2 = new Date().getTime();//当前时间的时间戳,单位:毫秒

        long sub =d1-d2;//这个是毫秒
        int seconds=(int) (sub/1000);//剩余秒数
        return seconds;
    }
}
