package tf.mcs.service.secu;

import org.bouncycastle.asn1.ASN1EncodableVector;
import org.bouncycastle.asn1.ASN1Integer;
import org.bouncycastle.asn1.DERSequence;
import org.bouncycastle.crypto.digests.SM3Digest;
import org.bouncycastle.util.encoders.Hex;
import tf.mcs.service.secu.sm2.SM2EncDecUtils;
import tf.mcs.service.secu.sm2.SM2KeyVO;
import tf.mcs.service.secu.sm2.SM2SignVO;
import tf.mcs.service.secu.sm2.SM2SignVerUtils;
import tf.mcs.service.secu.sm4.SM4Utils;
import tf.mcs.service.secu.sm4.SM4Utils_long;

import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.UUID;

public class SecurityTestAll {
    //SM2公钥编码格式
    //HardPubKey:3059301306072A8648CE3D020106082A811CCF5501822D03420004+X+Y
    //SoftPubKey:04+X+Y
    public static final String SM2PubHardKeyHead = "3059301306072A8648CE3D020106082A811CCF5501822D034200";
    //SM2加密 密文区别:软加密多了04
    //SM2加密机签名编码格式
    //HardSign:R+S
    //public static final String SM2PubHardKeyHead="3059301306072A8648CE3D020106082A811CCF5501822D034200";
    //private final String SM4_CBC_IV="";
    //private final String SM2="";


    public static void main(String[] args) throws Exception {
//        System.out.println("--产生SM2秘钥--:");
//        SM2KeyVO sm2KeyVO = generateSM2Key();
//        System.out.println("公钥:" + sm2KeyVO.getPubHexInSoft());
//        System.out.println("私钥:" + sm2KeyVO.getPriHexInSoft());
//
//        //数据加密
//        System.out.println("--测试加密开始--");
        String key = "F6A071B959E355EC5566BB537AC0EFEE";
        String src = "数据加密";
        String pub = "0466ca23e34a7eaea3d35fda722688568f224e6311e676f5b665c39bfd57e1d1e76c46bbcefa9f1f6302d7ad226a585a833b30c5f99c353b9e5f3f26c22b0748b5";
        String pri = "0ba3a6302be4335221b6cdfa64930720f9ff2abcf05cd42c52a8a58edb1f9909";
        //        String pub = sm2KeyVO.getPubHexInSoft();
//        String pri = sm2KeyVO.getPriHexInSoft();
//        System.out.println("公钥长度:" + pub.length());
//        System.out.println("原文UTF-8转hex:" + Util.byteToHex(src.getBytes()));
//        String SM2Enc = SM2Enc(pub, src);
//        System.out.println("密文:" + SM2Enc);
//        System.out.println("密文:" + SM2Enc.length());
//        String SM2Dec = SM2Dec(pri, SM2Enc);
//        System.out.println("解密:" + SM2Dec);
//        System.out.println("--测试加密结束--");
//
//        System.out.println("--测试SM2签名--");
//        System.out.println("原文hex:" + Util.byteToHex(src.getBytes()));
//        String s5 = Util.byteToHex(src.getBytes());
//
//        System.out.println("签名测试开始:");
//        SM2SignVO sign = genSM2Signature(pri, s5);
//        System.out.println("软加密签名结果:" + sign.getSm2_signForSoft());
//        System.out.println("加密机签名结果:" + sign.getSm2_signForHard());
//
//        //System.out.println("转签名测试:"+SM2SignHardToSoft(sign.getSm2_signForHard()));
//        System.out.println("验签1,软件加密方式:");
//        boolean b = verifySM2Signature(pub, s5, sign.getSm2_signForSoft());
//        System.out.println("软件加密方式验签结果:" + b);
//        System.out.println("验签2,硬件加密方式:");
//        String sm2_signForHard = sign.getSm2_signForHard();
//        System.out.println("签名R:"+sign.sign_r);
//        System.out.println("签名S:"+sign.sign_s);
//        System.out.println("硬:"+sm2_signForHard);
//        b = verifySM2Signature(sm2KeyVO.getPubHexInSoft(), s5, SM2SignHardToSoft(sign.getSm2_signForHard()));
//        System.out.println("硬件加密方式验签结果:" + b);
//        if (!b) {
//            throw new RuntimeException();
//        }
//        System.out.println("--签名测试结束--");
//
//        System.out.println("--SM3摘要测试--");
//        String s = generateSM3HASH(src);
//        System.out.println("hash:"+s);
//        System.out.println("--SM3摘要结束--");
//
//        System.out.println("--生成SM4秘钥--");
//        String sm4Key = generateSM4Key();
//        String sm4Key = "674166CA07F2F5521C47945452FC72A7";
//        System.out.println(sm4Key);
//        System.out.println("sm4Key:"+sm4Key);
//        System.out.println("--生成SM4结束--");
//        System.out.println("--SM4的CBC加密--");
//        String s1 = SM4EncForCBC(sm4Key, src);
//        System.out.println("密文:"+s1);
//        System.out.println("CBC解密");
//        String s2 = SM4DecForCBC(sm4Key, s1);
//        System.out.println("解密结果:"+s2);
//        System.out.println("--ECB加密--");
//        String s3 = SM4EncForECB(sm4Key, src);
//        System.out.println("ECB密文:"+s3);
//        System.out.println("ECB解密");
//        String s4 = SM4DecForECB(sm4Key, s3);
//        System.out.println("ECB解密结果:"+s4);
//

//
//        String key = "40404040404040405151515151515151";
//        String zmk = "674166CA07F2F5521C47945452FC72A7";
//        String deZmk = "018BC7DC13E4129AD4FAFE528AE6E365";
//
//        String zek = "B6D2C36DEFE9E5BB35B5012CCE6416A2";
//        String test = "00000000000000000000000000000000";
//
//        String deZek = "51F57A20A67814DE711CF58993E807F8";
//        String src = "A54E056412DE01B53E65BA4922FAACEE51A51AC55244CB5F18D71CAFD27C1957C8D9720ED869E09F2DD9FE1EE9B1CC876010848F64DE3CD5F62BCC832967DF7D3FC8FC56A2DEB56969C8CE162C7A9A1840E6C3CD05079CAEF59B7D8B1404CE8F01A3E03D64EB8DAA0EF5D4433378E1111879544BB62ECD9C2E0C842B7AAF484917B7902D104B3BA33C099537C78D23936E4699300F5C9ACBF6544F7365079BCEBECA176AD0F65A0B018ACC5B100D6CA63261F9095F06E6683D6B4269FBB78D162C1D3D0A8BCCACEB18549421039E5FF98204F19D587BE37E487E9B4A2FC068949629C65B9EC744EC7F8F6F1FECCF8C7BA4753880B049E1F61EEB115A9740F2AE35543C3FACB4C5D897A2E7A4AB11015DEB21942B784EAE95DECD5A24ED551A06A4CBECE2C95887FFE11AAAE8D47404588EF5BD76A0EDCBB20BEC9A6EC7EE8CD960AB757559FC872A757DFD16B669ADDCD6C4002C73EE42A8E25BB02535D0F627534B0DF52737A13897E78CC65BC9680584A0EB5308A0E334B2FFB96E3BA4364B9B4058B89F8DE6062A2E9A1500557FB362A6F4C9D1812C7E5EDC05C6AA1240035204B81CA41302FC2803AC2E3251AC341AD5F34542FFC371F8B550DD3EDE00BD79DB10472C0741F949F640CC498909DCD8A0DC29B9CD5D0ABD19E5F4497CA8AB06A15F7F87DB76B28EF28D77DF987A8FE97F4EFD40579F804259F81F82AE03D633A813D0A990A000493BC06F9FEB5733684257F0EDE3101548A45857A7B70FE4485FEA1799F2B53593EB712DCD0D3A091FFDFFB286B5A6D8A5F1BF6BC00002CFBA7363A0D9DCF131B46A591F015C55C4A88DAB186AAB982E7F969B75ED80AC0F6E873DFFC3C5F94DBCA922EFCF85E60A473F837C6FBADBA7C373E15A2F1068251627A77DF3378BF2490358C1B2D44F9ED2F1678F998D70CC6E88F7114A244464207AB38132265EF42AB6D6F3E94EB4F79EFB7FDBB1FA79B2618F0B8AA05B1D03E2D4D02054EB9968A735F06FDB40F3E53A13208CDB00E6C85D990BD117190BD44A01330687E6F71504B96798B0C19C76CC996B99CC17C3D9C0E5554018AB5A114865EABE162D52FAB2DD83384082442B";


//        System.out.println("--ECB加密 long--");
//        String s5 = SM4EncForECB_long(deZek, test,false);
//        String s5 = SM4EncForECB(key, src);
//        System.out.println("ECB密文:"+s5);
        String s4 = SM4DecForECB(key,"35468d27ce0fda1770be6ef63ec1d4ce");
        System.out.println("ECB明文:"+s4);
//        System.out.println("ECB密文长度:"+s5.length());
//        System.out.println("--ECB解密 long--");
//        String s4 = SM4DecForECB_Hex_long(key, zmk,false);
//        String s4 = SM4DecForECB_Hex_long(deZmk, zek,false);
//        String s4 = SM4DecForECB_long(deZek, src, false);
//        System.out.println("--ECB解密结果 long:"+s4);
//        System.out.println("--ECB解密结果 long:"+Util.hexStringToBytes(s4));
//        System.out.println("--ECB解密结果长度:"+s4.length());
//        HashMap hMap = new HashMap();
//        hMap.put("cliname","那莎");
//        hMap.put("certno","21020322222222");
//        RespTfData reqdata = new RespTfData();
//        reqdata.setRespCode("0000");
//        reqdata.setParmMap(hMap);
//        Tools.multiDecrypt(JSONObject.toJSONString(reqdata));
//        byte[] CK = vi.getBytes();
//        for(int i=0;i<CK.length;i++){
//            System.out.format( "0x%x,", CK[i] );
//        }
    }

    //产生对称秘钥
    public static String SM4KeyGenerator() throws NoSuchAlgorithmException {
        KeyGenerator kg = KeyGenerator.getInstance("SM4");
        kg.init(new SecureRandom());
        SecretKey keye = kg.generateKey();
        byte[] sm4k = keye.getEncoded();
        SecretKey keyd = new SecretKeySpec(sm4k, "SM4");
        return Util.byte2hex(keyd.getEncoded());
    }

    //SM2公钥soft和Hard转换
    public static String SM2PubKeySoftToHard(String softKey) {
        return SM2PubHardKeyHead + softKey;
    }

    //SM2公钥Hard和soft转换
    public static String SM2PubKeyHardToSoft(String hardKey) {
        return hardKey.replaceFirst(SM2PubHardKeyHead, "");
    }

    //产生非对称秘钥
    public static SM2KeyVO generateSM2Key() throws IOException {
        SM2KeyVO sm2KeyVO = SM2EncDecUtils.generateKeyPair();
        return sm2KeyVO;
    }

    //公钥加密
    public static String SM2Enc(String pubKey, String src) throws IOException {
        String encrypt = SM2EncDecUtils.encrypt(Util.hexStringToBytes(pubKey), src.getBytes());
        //删除04
        encrypt=encrypt.substring(2);
        return encrypt;
    }

    //私钥解密
    public static String SM2Dec(String priKey, String encryptedData) throws IOException {
        //填充04
        encryptedData="04"+encryptedData;
        byte[] decrypt = SM2EncDecUtils.decrypt(Util.hexStringToBytes(priKey), Util.hexStringToBytes(encryptedData));
        return new String(decrypt);
    }

    //私钥签名,参数二:原串必须是hex!!!!因为是直接用于计算签名的,可能是SM3串,也可能是普通串转Hex
    public static SM2SignVO genSM2Signature(String priKey, String sourceData) throws Exception {
        SM2SignVO sign = SM2SignVerUtils.Sign2SM2(Util.hexToByte(priKey), Util.hexToByte(sourceData));
        return sign;
    }

    //公钥验签,参数二:原串必须是hex!!!!因为是直接用于计算签名的,可能是SM3串,也可能是普通串转Hex
    public static boolean verifySM2Signature(String pubKey, String sourceData, String hardSign) {
        SM2SignVO verify = SM2SignVerUtils.VerifySignSM2(Util.hexStringToBytes(pubKey), Util.hexToByte(sourceData), Util.hexToByte(hardSign));
        return verify.isVerify();
    }

    //SM2签名Hard转soft
    public static String SM2SignHardToSoft(String hardSign) {
        byte[] bytes = Util.hexToByte(hardSign);
        byte[] r = new byte[bytes.length / 2];
        byte[] s = new byte[bytes.length / 2];
        System.arraycopy(bytes, 0, r, 0, bytes.length / 2);
        System.arraycopy(bytes, bytes.length / 2, s, 0, bytes.length / 2);
        ASN1Integer d_r = new ASN1Integer(Util.byteConvertInteger(r));
        ASN1Integer d_s = new ASN1Integer(Util.byteConvertInteger(s));
        ASN1EncodableVector v2 = new ASN1EncodableVector();
        v2.add(d_r);
        v2.add(d_s);
        DERSequence sign = new DERSequence(v2);

        String result = null;
        try {
            result = Util.byteToHex(sign.getEncoded());
        } catch (IOException e) {
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        //SM2加密机转软加密编码格式
        //return SM2SignHardKeyHead+hardSign.substring(0, hardSign.length()/2)+SM2SignHardKeyMid+hardSign.substring(hardSign.length()/2);
        return result;
    }

    //摘要计算
    public static String generateSM3HASH(String src) {
        byte[] md = new byte[32];
        byte[] msg1 = src.getBytes();
        //System.out.println(Util.byteToHex(msg1));
        SM3Digest sm3 = new SM3Digest();
        sm3.update(msg1, 0, msg1.length);
        sm3.doFinal(md, 0);
        String s = new String(Hex.encode(md));
        return s.toUpperCase();
    }

    //产生对称秘钥
    public static String generateSM4Key() {
        return UUID.randomUUID().toString().replace("-", "").toUpperCase();
    }


    //对称秘钥加密(CBC)
    public static String SM4EncForCBC(String key,String text) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = key;
        sm4.hexString = true;
        sm4.iv = "31313131313131313131313131313131";
        String cipherText = sm4.encryptData_CBC(text);
        return cipherText;
    }

    //对称秘钥解密(CBC)
    public static String SM4DecForCBC(String key,String text) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = key;
        sm4.hexString = true;
        sm4.iv = "31313131313131313131313131313131";
        String plainText = sm4.decryptData_CBC(text);
        return plainText;
    }
    //对称秘钥加密(ECB)
    public static String SM4EncForECB(String key,String text) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = key;
        sm4.hexString = true;
        String cipherText = sm4.encryptData_ECB(text);

        return cipherText;
    }

    //对称秘钥加密(ECB)
    public static String SM4EncForECB_byte(String key,byte[] bytes) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = key;
        sm4.hexString = true;
        String cipherText = sm4.encryptData_ECB(bytes);

        return cipherText;
    }

    //对称秘钥解密(ECB)
    public static String SM4DecForECB(String key,String text) {
        SM4Utils sm4 = new SM4Utils();
        sm4.secretKey = key;
        sm4.hexString = true;
        String plainText = sm4.decryptData_ECB(text);
        return plainText;
    }
    //对称秘钥加密(ECB)
    public static String SM4EncForECB_long(String key,String text,boolean isPadding) {
        SM4Utils_long sm4 = new SM4Utils_long();
        sm4.secretKey = key;
        sm4.hexString = true;
        String cipherText = sm4.encryptData_ECB(text,isPadding);

        return cipherText;
    }
    //对称秘钥解密(ECB)
    public static String SM4DecForECB_long(String key,String text,boolean isPadding) {
        SM4Utils_long sm4 = new SM4Utils_long();
        sm4.secretKey = key;
        sm4.hexString = true;
        String plainText = sm4.decryptData_ECB(text,isPadding);
        return plainText;
    }
    //对称秘钥解密(ECB)
    public static String SM4DecForECB_Hex_long(String key,String text,boolean isPadding) {
        SM4Utils_long sm4 = new SM4Utils_long();
        sm4.secretKey = key;
        sm4.hexString = true;
        sm4.resultHex = true;
        String plainText = sm4.decryptData_ECB(text,isPadding);
        return plainText;
    }

    /**
     * 报文加签
     * @param data
     * @param MD5key
     * @return
     * @throws Exception
     */
    public static String getSignature(String data, String MD5key)
            throws Exception {
        data = data + "|" + MD5key;
        MessageDigest md = MessageDigest.getInstance("MD5");
        byte[] b = md.digest(data.getBytes("UTF-8"));
        return Base64.getEncoder().encodeToString(b);
    }

}
