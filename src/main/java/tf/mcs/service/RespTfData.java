package tf.mcs.service;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * dubbo服务端站点响应类
 * 
 * <AUTHOR>
 * @version v1.0 2016-12-2
 */
public class RespTfData implements Serializable {

	private static final long serialVersionUID = -6536514856560958042L;

	private String respCode;
	private String respDesc;
	private String serid;

	public String getSerid() {
		return serid;
	}

	public void setSerid(String serid) {
		this.serid = serid;
	}

	public Map<String, Object> parmMap = new HashMap<String, Object>();

	public Map<String, Object> getParmMap() {
		return parmMap;
	}

	public void setParmMap(Map<String, Object> parmMap) {
		this.parmMap = parmMap;
	}

	public void setRespEntity(String key, Object entity) {
		parmMap.put(key, entity);
	}

	public Object getRespEntity(String key) {
		return parmMap.get(key);
	}

	public String getRespCode() {
		return respCode;
	}

	public void setRespCode(String respCode) {
		this.respCode = respCode;
	}

	public String getRespDesc() {
		return respDesc;
	}

	public void setRespDesc(String respDesc) {
		this.respDesc = respDesc;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((parmMap == null) ? 0 : parmMap.hashCode());
		result = prime * result + ((respCode == null) ? 0 : respCode.hashCode());
		result = prime * result + ((respDesc == null) ? 0 : respDesc.hashCode());
		result = prime * result + ((serid == null) ? 0 : serid.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		RespTfData other = (RespTfData) obj;
		if (parmMap == null) {
			if (other.parmMap != null)
				return false;
		} else if (!parmMap.equals(other.parmMap))
			return false;
		if (respCode == null) {
			if (other.respCode != null)
				return false;
		} else if (!respCode.equals(other.respCode))
			return false;
		if (respDesc == null) {
			if (other.respDesc != null)
				return false;
		} else if (!respDesc.equals(other.respDesc))
			return false;
		if (serid == null) {
			if (other.serid != null)
				return false;
		} else if (!serid.equals(other.serid))
			return false;
		return true;
	}

	@Override
	public String toString() {
		return "RespTfData [respCode=" + respCode + ", respDesc=" + respDesc + ", serid=" + serid + ", parmMap="
				+ parmMap + "]";
	}

}
