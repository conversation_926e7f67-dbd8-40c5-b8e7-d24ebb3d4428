workflow:
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" &&
        ($APP == "tfrunningGW") &&
        ($ENV == "qa" || $ENV == "uat" || $ENV == "prod")
      when: always
    - when: never

variables:
  APP:
    value: GL-Service
    description: 应用名称
    options:
      - tfrunningGW

  ENV:
    value: qa
    description: 部署环境
    options:
      - qa
      - uat
      - prod

stages:
  - check_role
  - build
  - deploy

# 检查用户权限阶段
check_role:
  stage: check_role
  image:
    name: alpine/curl:8.12.1
    pull_policy: if-not-present
  variables:
    GIT_STRATEGY: none
  script:
    - |
      # 使用GitLab API获取用户在项目中的角色
      PROJECT_ID=$CI_PROJECT_ID
      USER_ID=$GITLAB_USER_ID
      
      # 检查API令牌是否存在
      if [ -z "$GITLAB_API_TOKEN" ]; then
        echo "错误: 未设置GITLAB_API_TOKEN变量"
        exit 1
      fi
      
      # 通过API获取用户角色
      echo "使用API令牌获取用户角色..."
      ROLE_RESPONSE=$(curl -sH "PRIVATE-TOKEN: $GITLAB_API_TOKEN" "http://********/api/v4/projects/$PROJECT_ID/members/all/$USER_ID")
      
      # 检查API响应是否有效
      if [ $? -ne 0 ] || [ -z "$ROLE_RESPONSE" ] || [[ "$ROLE_RESPONSE" == *"error"* ]]; then
        echo "错误: API请求失败或返回错误"
        exit 1
      fi
      
      # 提取access_level字段
      ACCESS_LEVEL=$(echo $ROLE_RESPONSE | grep -o '"access_level":[0-9]*' | grep -o '[0-9]*')
      
      # 检查是否成功提取到access_level
      if [ -z "$ACCESS_LEVEL" ]; then
        echo "错误: API响应解析失败或用户在项目中没有角色"
        exit 1
      fi
      
      echo "用户ID: $USER_ID, 访问级别: $ACCESS_LEVEL"
      
      # 检查生产环境权限
      if [ "$ENV" = "prod" ] && [ "$ACCESS_LEVEL" -lt 40 ]; then
        echo "错误: 生产环境部署需要 Maintainer(40) 或更高权限，您的权限级别是: $ACCESS_LEVEL"
        exit 1
      fi
      
      # 检查QA/UAT环境权限
      if [ "$ENV" = "qa" -o "$ENV" = "uat" ] && [ "$ACCESS_LEVEL" -lt 30 ]; then
        echo "错误: QA/UAT环境部署需要 Developer(30) 或更高权限，您的权限级别是: $ACCESS_LEVEL"
        exit 1
      fi
      
      echo "用户角色检查通过，继续部署"
  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always

build:
  stage: build
  image:
    name: iisimpler/jdk8-maven-builder:8u431-3.9.9
    pull_policy: if-not-present
  variables:
    GIT_DEPTH: 1
    LANG: C.UTF-8
    LC_ALL: C.UTF-8
  script:
    - git clone http://$DEPLOY_TOKEN_USERNAME:$DEPLOY_TOKEN@********/config/tfrunningGW.git config-repo
    - |
      echo "Copying config files to $APP/src/main/resources/"
      cp -rf config-repo/$APP/$ENV/* $APP/src/main/resources/
    - mvn clean package -pl $APP -am -P package -DskipTests
  artifacts:
    paths:
      - target/$APP.tgz
    expire_in: 60 min

  rules:
    - if: $CI_PIPELINE_SOURCE == "web"
      when: on_success
  needs:
    - job: check_role
      artifacts: false

.deploy-tgz-template: &deploy-tgz
  script:
    - ssh -i $PEM_PATH -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST "mkdir -p $APP_PATH/$APP && mkdir -p $APP_PATH/bak"

    # 备份现有包
    - ssh -i $PEM_PATH -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST "if [ -f $APP_PATH/$APP/$APP.tgz ]; then mv $APP_PATH/$APP/$APP.tgz $APP_PATH/bak; fi"

    # 清理现有服务目录
    - ssh -i $PEM_PATH -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST "rm -rf $APP_PATH/$APP/*"

    # 拷贝新包并解压
    - scp -i $PEM_PATH -o StrictHostKeyChecking=no $APP/target/$APP.tgz $REMOTE_USER@$REMOTE_HOST:$APP_PATH/$APP/
    - ssh -i $PEM_PATH -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST "cd $APP_PATH/$APP && tar xzf $APP.tgz"

    # 重启服务
    - |
      ssh -t -i $PEM_PATH -o StrictHostKeyChecking=no $REMOTE_USER@$REMOTE_HOST "
      # 获取旧进程ID
      OLD_PID=\$(ps -u $REMOTE_USER -f | grep $APP | grep -vE 'bash|grep' | awk '{print \$2}' || echo 'not_running')
      echo \"Old process ID: \$OLD_PID\"

      # 重启服务
      sudo systemctl daemon-reload
      sudo systemctl restart $APP
      echo \"Service restart command issued...\"

      # 等待新进程启动（最多等待30秒）
      COUNTER=0
      while [ \$COUNTER -lt 10 ]; do
        NEW_PID=\$(ps -u $REMOTE_USER -f | grep $APP | grep -vE 'bash|grep' | awk '{print \$2}' || echo 'not_running')
        if [ \"\$NEW_PID\" != \"\$OLD_PID\" ] && [ \"\$NEW_PID\" != 'not_running' ]; then
          echo \"New process detected with PID: \$NEW_PID\"
          break
        fi
        echo \"Waiting for $APP to start... \$((\$COUNTER * 3))s\"
        sleep 3
        COUNTER=\$((\$COUNTER + 1))
      done

      if [ \$COUNTER -eq 10 ]; then
        echo \"Error: Service failed to start with new PID within 30 seconds\"
        exit 1
      fi

      # Double check端口可访问性
      echo \"Verifying service health...\"
      COUNTER=0
      HEALTH_URL=\"http://localhost:$APP_PORT${APP_CONTEXT_PATH:+/$APP_CONTEXT_PATH}/health/check\"
      echo \"HEALTH_URL: \$HEALTH_URL\"
      while [ \$COUNTER -lt 40 ]; do
        RESPONSE=\$(curl -s -w \"|||%{http_code}\" \"\$HEALTH_URL\" 2>/dev/null)
        BODY=\"\${RESPONSE%|||*}\"
        HTTP_CODE=\"\${RESPONSE##*|||}\"
        # 去除响应中可能的换行符
        BODY_CLEAN=\$(echo \"\$BODY\" | tr -d '\\n\\r')
        echo \"$APP health check: \$((\$COUNTER * 3))s, code: \$HTTP_CODE, body: \$BODY_CLEAN\"
        if [ \"\$HTTP_CODE\" = \"200\" ] && [ \"\$BODY_CLEAN\" = \"OK\" ]; then
          echo \"$APP is healthy on \$HEALTH_URL\"
          exit 0
        fi
        sleep 3
        COUNTER=\$((\$COUNTER + 1))
      done

      echo \"Error: Health check \$HEALTH_URL failed after 120 seconds\"
      exit 1
      "

deploy-tfrunningGW-qa:
  stage: deploy
  image:
    name: iisimpler/debian:bullseye-slim-ssh
    pull_policy: if-not-present
  <<: *deploy-tgz
  variables:
    GIT_STRATEGY: none
    REMOTE_USER: "mcsops"
    REMOTE_HOST: "*************"
    APP_PATH: "/home/<USER>/app"
    APP_PORT: "8061"
    PEM_PATH: "/root/pem/qa.pem"
    APP_CONTEXT_PATH: "tfrunningGW"
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $APP == "tfrunningGW" && $ENV == "qa"
      when: on_success
  needs:
    - job: build
      artifacts: true

deploy-tfrunningGW-uat:
  stage: deploy
  image:
    name: iisimpler/debian:bullseye-slim-ssh
    pull_policy: if-not-present
  <<: *deploy-tgz
  variables:
    GIT_STRATEGY: none
    REMOTE_USER: "mcsops"
    REMOTE_HOST: "*************"
    APP_PATH: "/home/<USER>/app"
    APP_PORT: "8061"
    PEM_PATH: "/root/pem/uat.pem"
    APP_CONTEXT_PATH: "tfrunningGW"
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $APP == "tfrunningGW" && $ENV == "uat"
      when: on_success
  needs:
    - job: build
      artifacts: true

deploy-tfrunningGW-prod:
  stage: deploy
  image:
    name: iisimpler/debian:bullseye-slim-ssh
    pull_policy: if-not-present
  <<: *deploy-tgz
  variables:
    GIT_STRATEGY: none
    REMOTE_USER: "mcsops"
    REMOTE_HOST: "**********"
    APP_PATH: "/home/<USER>/app"
    APP_PORT: "8050"
    PEM_PATH: "/root/pem/mis_web.pem"
    APP_CONTEXT_PATH: "tfrunningGW"
  rules:
    - if: $CI_PIPELINE_SOURCE == "web" && $APP == "tfrunningGW" && $ENV == "prod"
      when: on_success
  needs:
    - job: build
      artifacts: true
