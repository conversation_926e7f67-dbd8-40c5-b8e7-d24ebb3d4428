### tf.mcs.service dubbo api 自主引入
```
mvn install:install-file  -Dfile=library/tfapi/ifmsService-1.0.1.jar  -DgroupId=com.tfrunning  -DartifactId=ifms-tf.mcs.service -Dversion=1.0.1 -Dpackaging=jar
```

### open-info
```
mvn install:install-file  -Dfile=library/lifeApp/open-info-V1.5.jar  -DgroupId=com.txlifeapp  -DartifactId=jnbank-service -Dversion=1.0.1 -Dpackaging=jar
```

### http 自主引入
```
mvn install:install-file  -Dfile=library/http/httpclient-4.2.2.jar  -DgroupId=http  -DartifactId=http-client -Dversion=4.2.2 -Dpackaging=jar
mvn install:install-file  -Dfile=library/http/httpcore-4.2.2.jar  -DgroupId=http  -DartifactId=http-core -Dversion=4.2.2 -Dpackaging=jar
mvn install:install-file  -Dfile=library/http/httpmime-4.2.2.jar  -DgroupId=comtfrunning  -DartifactId=http-mime -Dversion=4.2.2 -Dpackaging=jar
mvn install:install-file  -Dfile=library/comm/commons-httpclient.jar  -DgroupId=comtfrunning  -DartifactId=commons-httpclient -Dversion=1.0.0 -Dpackaging=jar
```